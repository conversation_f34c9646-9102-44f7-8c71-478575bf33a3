# Agent与工具链

## 📋 模块概述

Agent与工具链是AI应用的高级形态，通过智能代理和工具集成实现复杂任务的自动化处理。本模块涵盖Function Calling、多Agent协作、端侧推理等前沿技术，为构建智能化应用提供完整的技术方案。

## 🎯 学习目标

通过学习本模块，您将能够：
- 掌握Function Calling和工具调用协议
- 理解多Agent协作的设计模式
- 熟练使用Agent开发框架和SDK
- 了解端侧推理和模型优化技术
- 具备构建复杂AI Agent系统的能力

## 📚 知识点列表

### 84. Function Call / Tool Use 协议（OpenAI、MCP）
**掌握程度**：前端声明 JSON Schema，动态生成表单

**技术解释与实现方案**：
Function Calling是大语言模型调用外部工具和API的标准协议，允许模型根据用户需求自动选择和执行相应的函数。它通过JSON Schema定义函数接口，实现了自然语言与程序化操作的桥接。

**核心算法原理**：
- **意图识别**：模型分析用户输入，识别需要调用的函数
- **参数提取**：从自然语言中提取函数调用所需的参数
- **函数执行**：根据提取的参数调用相应的函数
- **结果整合**：将函数执行结果整合到对话回复中

**技术实现方案**：
```javascript
// Function Calling 管理器
class FunctionCallManager {
  constructor() {
    this.functions = new Map();
    this.schemas = new Map();
  }
  
  registerFunction(name, func, schema) {
    this.functions.set(name, func);
    this.schemas.set(name, schema);
  }
  
  getFunctionSchemas() {
    return Array.from(this.schemas.entries()).map(([name, schema]) => ({
      name,
      ...schema
    }));
  }
  
  async executeFunction(functionCall) {
    const { name, arguments: args } = functionCall;
    
    if (!this.functions.has(name)) {
      throw new Error(`Function ${name} not found`);
    }
    
    const func = this.functions.get(name);
    const schema = this.schemas.get(name);
    
    // 验证参数
    const validatedArgs = this.validateArguments(args, schema);
    
    try {
      const result = await func(validatedArgs);
      return {
        success: true,
        result,
        functionName: name
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        functionName: name
      };
    }
  }
  
  validateArguments(args, schema) {
    const { parameters } = schema;
    const validated = {};
    
    // 检查必需参数
    if (parameters.required) {
      for (const required of parameters.required) {
        if (!(required in args)) {
          throw new Error(`Missing required parameter: ${required}`);
        }
      }
    }
    
    // 验证参数类型
    for (const [key, value] of Object.entries(args)) {
      const paramSchema = parameters.properties[key];
      if (paramSchema) {
        validated[key] = this.validateParameter(value, paramSchema);
      }
    }
    
    return validated;
  }
  
  validateParameter(value, schema) {
    switch (schema.type) {
      case 'string':
        return String(value);
      case 'number':
        const num = Number(value);
        if (isNaN(num)) throw new Error(`Invalid number: ${value}`);
        return num;
      case 'boolean':
        return Boolean(value);
      case 'array':
        if (!Array.isArray(value)) throw new Error(`Expected array, got ${typeof value}`);
        return value;
      case 'object':
        if (typeof value !== 'object') throw new Error(`Expected object, got ${typeof value}`);
        return value;
      default:
        return value;
    }
  }
}

// 使用示例
const functionManager = new FunctionCallManager();

// 注册天气查询函数
functionManager.registerFunction('get_weather', async (args) => {
  const { city } = args;
  // 模拟API调用
  return {
    city,
    temperature: 22,
    condition: 'sunny',
    humidity: 65
  };
}, {
  description: 'Get current weather information for a city',
  parameters: {
    type: 'object',
    properties: {
      city: {
        type: 'string',
        description: 'The city name to get weather for'
      }
    },
    required: ['city']
  }
});

// 与LLM集成
async function chatWithFunctions(message) {
  const response = await fetch('/api/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      messages: [{ role: 'user', content: message }],
      functions: functionManager.getFunctionSchemas()
    })
  });
  
  const data = await response.json();
  
  if (data.function_call) {
    // 执行函数调用
    const result = await functionManager.executeFunction(data.function_call);
    
    // 将结果返回给模型
    const followUpResponse = await fetch('/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: message },
          { role: 'assistant', content: null, function_call: data.function_call },
          { role: 'function', name: data.function_call.name, content: JSON.stringify(result) }
        ]
      })
    });
    
    return followUpResponse.json();
  }
  
  return data;
}
```

**技术要点**：
- JSON Schema设计和验证
- 函数注册和管理机制
- 参数提取和类型转换
- 错误处理和异常管理

**实践项目**：
- 开发Function Calling框架
- 实现工具调用可视化界面
- 构建函数库管理系统

### 85. 多 Agent 协作协议（A2A、AutoGen GroupChat）
**掌握程度**：前端可视化编排节点连线

**技术解释与实现方案**：
多Agent协作是通过多个智能代理的协同工作来完成复杂任务的技术。每个Agent具有特定的角色和能力，通过标准化的通信协议进行信息交换和任务协调。

**核心算法原理**：
- **角色分工**：不同Agent承担不同的专业角色和职责
- **通信协议**：定义Agent间的消息格式和交互规则
- **任务调度**：协调多个Agent的执行顺序和依赖关系
- **状态同步**：维护全局状态的一致性和可见性

**技术实现方案**：
```javascript
// 多Agent协作框架
class MultiAgentSystem {
  constructor() {
    this.agents = new Map();
    this.messageQueue = [];
    this.globalState = {};
    this.workflows = new Map();
  }
  
  registerAgent(id, agent) {
    this.agents.set(id, agent);
    agent.setSystem(this);
  }
  
  async sendMessage(fromId, toId, message) {
    const targetAgent = this.agents.get(toId);
    if (!targetAgent) {
      throw new Error(`Agent ${toId} not found`);
    }
    
    const messageObj = {
      id: this.generateMessageId(),
      from: fromId,
      to: toId,
      content: message,
      timestamp: Date.now()
    };
    
    this.messageQueue.push(messageObj);
    return targetAgent.receiveMessage(messageObj);
  }
  
  async broadcast(fromId, message, excludeIds = []) {
    const promises = [];
    
    for (const [agentId, agent] of this.agents) {
      if (agentId !== fromId && !excludeIds.includes(agentId)) {
        promises.push(this.sendMessage(fromId, agentId, message));
      }
    }
    
    return Promise.all(promises);
  }
  
  async executeWorkflow(workflowId, input) {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }
    
    let currentData = input;
    const executionLog = [];
    
    for (const step of workflow.steps) {
      const agent = this.agents.get(step.agentId);
      if (!agent) {
        throw new Error(`Agent ${step.agentId} not found in workflow`);
      }
      
      try {
        const result = await agent.execute(step.task, currentData);
        currentData = result;
        
        executionLog.push({
          step: step.name,
          agentId: step.agentId,
          input: currentData,
          output: result,
          timestamp: Date.now()
        });
      } catch (error) {
        executionLog.push({
          step: step.name,
          agentId: step.agentId,
          error: error.message,
          timestamp: Date.now()
        });
        throw error;
      }
    }
    
    return {
      result: currentData,
      executionLog
    };
  }
  
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Agent基类
class BaseAgent {
  constructor(id, role, capabilities = []) {
    this.id = id;
    this.role = role;
    this.capabilities = capabilities;
    this.system = null;
    this.memory = [];
  }
  
  setSystem(system) {
    this.system = system;
  }
  
  async receiveMessage(message) {
    this.memory.push(message);
    return this.processMessage(message);
  }
  
  async processMessage(message) {
    // 子类实现具体的消息处理逻辑
    throw new Error('processMessage must be implemented by subclass');
  }
  
  async execute(task, data) {
    // 子类实现具体的任务执行逻辑
    throw new Error('execute must be implemented by subclass');
  }
  
  async sendMessage(toId, content) {
    if (!this.system) {
      throw new Error('Agent not registered to system');
    }
    return this.system.sendMessage(this.id, toId, content);
  }
}

// 具体Agent实现示例
class ResearchAgent extends BaseAgent {
  constructor(id) {
    super(id, 'researcher', ['web_search', 'data_analysis']);
  }
  
  async processMessage(message) {
    if (message.content.includes('research')) {
      return this.conductResearch(message.content);
    }
    return null;
  }
  
  async execute(task, data) {
    switch (task.type) {
      case 'research':
        return this.conductResearch(task.query);
      case 'analyze':
        return this.analyzeData(data);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }
  
  async conductResearch(query) {
    // 模拟研究过程
    return {
      query,
      findings: ['Finding 1', 'Finding 2', 'Finding 3'],
      sources: ['Source A', 'Source B'],
      confidence: 0.85
    };
  }
  
  async analyzeData(data) {
    // 模拟数据分析
    return {
      summary: 'Data analysis complete',
      insights: ['Insight 1', 'Insight 2'],
      recommendations: ['Recommendation 1']
    };
  }
}

// 使用示例
const system = new MultiAgentSystem();

// 注册Agent
const researcher = new ResearchAgent('researcher-001');
const writer = new WriterAgent('writer-001');
const reviewer = new ReviewerAgent('reviewer-001');

system.registerAgent('researcher-001', researcher);
system.registerAgent('writer-001', writer);
system.registerAgent('reviewer-001', reviewer);

// 定义工作流
system.workflows.set('content-creation', {
  name: 'Content Creation Workflow',
  steps: [
    { name: 'Research', agentId: 'researcher-001', task: { type: 'research' } },
    { name: 'Write', agentId: 'writer-001', task: { type: 'write' } },
    { name: 'Review', agentId: 'reviewer-001', task: { type: 'review' } }
  ]
});

// 执行工作流
const result = await system.executeWorkflow('content-creation', {
  topic: 'AI in Healthcare',
  requirements: ['1000 words', 'academic tone']
});
```

**技术要点**：
- Agent角色设计和能力定义
- 消息传递和状态管理
- 工作流编排和执行
- 可视化界面设计

**实践项目**：
- 开发多Agent协作平台
- 实现可视化工作流编辑器
- 构建Agent性能监控系统

### 86. 前端 Agent SDK（@cloudbase/aiagent-framework、AG-UI Client）
**掌握程度**：会初始化、注册工具、监听生命周期事件

**技术解释与实现方案**：
前端Agent SDK提供了在浏览器环境中构建和运行AI Agent的完整工具集。它封装了Agent的生命周期管理、工具注册、事件处理等核心功能，让前端开发者能够快速构建智能化应用。

**核心算法原理**：
- **生命周期管理**：管理Agent的初始化、运行、暂停、销毁等状态
- **工具注册机制**：动态注册和管理Agent可用的工具和API
- **事件驱动架构**：通过事件系统实现组件间的解耦通信
- **状态持久化**：保存和恢复Agent的运行状态

**技术实现方案**：
```javascript
// 前端Agent SDK实现
class FrontendAgentSDK {
  constructor(config) {
    this.config = config;
    this.agents = new Map();
    this.tools = new Map();
    this.eventBus = new EventEmitter();
    this.state = 'idle';
  }

  async initialize() {
    this.state = 'initializing';

    // 初始化基础工具
    await this.registerBuiltinTools();

    // 连接到后端服务
    if (this.config.backendUrl) {
      await this.connectToBackend();
    }

    this.state = 'ready';
    this.eventBus.emit('sdk:ready');
  }

  async createAgent(agentConfig) {
    const agent = new FrontendAgent({
      ...agentConfig,
      sdk: this,
      tools: this.tools
    });

    await agent.initialize();
    this.agents.set(agent.id, agent);

    this.eventBus.emit('agent:created', { agentId: agent.id });
    return agent;
  }

  registerTool(name, tool) {
    this.tools.set(name, tool);
    this.eventBus.emit('tool:registered', { name, tool });
  }

  async registerBuiltinTools() {
    // DOM操作工具
    this.registerTool('dom_query', {
      name: 'dom_query',
      description: 'Query DOM elements',
      execute: async (selector) => {
        const elements = document.querySelectorAll(selector);
        return Array.from(elements).map(el => ({
          tagName: el.tagName,
          textContent: el.textContent,
          attributes: Object.fromEntries(
            Array.from(el.attributes).map(attr => [attr.name, attr.value])
          )
        }));
      }
    });

    // HTTP请求工具
    this.registerTool('http_request', {
      name: 'http_request',
      description: 'Make HTTP requests',
      execute: async ({ url, method = 'GET', headers = {}, body }) => {
        const response = await fetch(url, {
          method,
          headers,
          body: body ? JSON.stringify(body) : undefined
        });
        return {
          status: response.status,
          headers: Object.fromEntries(response.headers.entries()),
          data: await response.json()
        };
      }
    });

    // 本地存储工具
    this.registerTool('local_storage', {
      name: 'local_storage',
      description: 'Access browser local storage',
      execute: async ({ action, key, value }) => {
        switch (action) {
          case 'get':
            return localStorage.getItem(key);
          case 'set':
            localStorage.setItem(key, value);
            return true;
          case 'remove':
            localStorage.removeItem(key);
            return true;
          case 'clear':
            localStorage.clear();
            return true;
          default:
            throw new Error(`Unknown action: ${action}`);
        }
      }
    });
  }

  on(event, callback) {
    this.eventBus.on(event, callback);
  }

  off(event, callback) {
    this.eventBus.off(event, callback);
  }

  emit(event, data) {
    this.eventBus.emit(event, data);
  }
}

// 前端Agent类
class FrontendAgent {
  constructor(config) {
    this.id = config.id || this.generateId();
    this.name = config.name;
    this.description = config.description;
    this.sdk = config.sdk;
    this.tools = config.tools;
    this.memory = [];
    this.state = 'created';
    this.context = {};
  }

  async initialize() {
    this.state = 'initializing';

    // 加载Agent配置
    if (this.config.configUrl) {
      await this.loadConfig();
    }

    // 初始化工具
    await this.initializeTools();

    this.state = 'ready';
    this.sdk.emit('agent:ready', { agentId: this.id });
  }

  async execute(task) {
    this.state = 'executing';
    this.sdk.emit('agent:execution:start', { agentId: this.id, task });

    try {
      const result = await this.processTask(task);

      this.state = 'ready';
      this.sdk.emit('agent:execution:complete', {
        agentId: this.id,
        task,
        result
      });

      return result;
    } catch (error) {
      this.state = 'error';
      this.sdk.emit('agent:execution:error', {
        agentId: this.id,
        task,
        error
      });
      throw error;
    }
  }

  async processTask(task) {
    // 分析任务需要的工具
    const requiredTools = await this.analyzeTaskTools(task);

    // 执行任务步骤
    const steps = await this.planTaskSteps(task, requiredTools);
    const results = [];

    for (const step of steps) {
      const stepResult = await this.executeStep(step);
      results.push(stepResult);

      // 更新上下文
      this.updateContext(step, stepResult);
    }

    return this.synthesizeResults(results);
  }

  async executeStep(step) {
    const tool = this.tools.get(step.tool);
    if (!tool) {
      throw new Error(`Tool ${step.tool} not found`);
    }

    this.sdk.emit('agent:step:start', {
      agentId: this.id,
      step
    });

    try {
      const result = await tool.execute(step.parameters);

      this.sdk.emit('agent:step:complete', {
        agentId: this.id,
        step,
        result
      });

      return result;
    } catch (error) {
      this.sdk.emit('agent:step:error', {
        agentId: this.id,
        step,
        error
      });
      throw error;
    }
  }

  generateId() {
    return `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 使用示例
const sdk = new FrontendAgentSDK({
  backendUrl: 'https://api.example.com/agent',
  apiKey: 'your-api-key'
});

// 初始化SDK
await sdk.initialize();

// 监听事件
sdk.on('agent:created', (data) => {
  console.log('Agent created:', data.agentId);
});

sdk.on('agent:execution:start', (data) => {
  console.log('Agent execution started:', data);
});

// 创建Agent
const agent = await sdk.createAgent({
  name: 'WebAssistant',
  description: 'A web automation assistant',
  capabilities: ['dom_manipulation', 'data_extraction']
});

// 执行任务
const result = await agent.execute({
  type: 'extract_data',
  target: 'product_list',
  selector: '.product-item',
  fields: ['name', 'price', 'rating']
});

console.log('Extraction result:', result);
```

**技术要点**：
- SDK架构设计和模块化
- 工具注册和管理机制
- 事件驱动的生命周期管理
- 浏览器环境适配

**实践项目**：
- 开发前端Agent SDK
- 实现Agent调试工具
- 构建Agent应用模板

### 87. 低代码可视化 Agent 构建器（拖拽式节点 + 代码沙箱）
**掌握程度**：扩展自定义节点组件

**技术解释与实现方案**：
低代码Agent构建器通过可视化的拖拽界面让非技术用户也能创建复杂的AI Agent工作流。它结合了节点编辑器、代码沙箱、模板系统等技术，提供了从设计到部署的完整解决方案。

**核心算法原理**：
- **节点图系统**：使用有向图表示Agent的工作流逻辑
- **代码生成**：将可视化配置转换为可执行的代码
- **沙箱执行**：在安全的环境中执行用户定义的逻辑
- **模板引擎**：提供可复用的Agent模板和组件

**技术实现方案**：
```javascript
// 可视化Agent构建器
class VisualAgentBuilder {
  constructor(container) {
    this.container = container;
    this.canvas = null;
    this.nodes = new Map();
    this.connections = [];
    this.nodeTypes = new Map();
    this.sandbox = new CodeSandbox();

    this.initializeCanvas();
    this.registerBuiltinNodes();
  }

  initializeCanvas() {
    this.canvas = new FlowCanvas(this.container);

    // 监听节点操作事件
    this.canvas.on('node:add', (nodeData) => {
      this.addNode(nodeData);
    });

    this.canvas.on('node:connect', (connection) => {
      this.addConnection(connection);
    });

    this.canvas.on('node:delete', (nodeId) => {
      this.deleteNode(nodeId);
    });
  }

  registerNodeType(type, definition) {
    this.nodeTypes.set(type, definition);
    this.canvas.addNodeType(type, definition);
  }

  registerBuiltinNodes() {
    // 输入节点
    this.registerNodeType('input', {
      name: 'Input',
      category: 'basic',
      inputs: [],
      outputs: [{ name: 'data', type: 'any' }],
      properties: {
        inputType: { type: 'select', options: ['text', 'file', 'json'] },
        defaultValue: { type: 'text' }
      },
      execute: async (inputs, properties) => {
        return { data: properties.defaultValue };
      }
    });

    // LLM节点
    this.registerNodeType('llm', {
      name: 'LLM',
      category: 'ai',
      inputs: [
        { name: 'prompt', type: 'string' },
        { name: 'context', type: 'string', optional: true }
      ],
      outputs: [{ name: 'response', type: 'string' }],
      properties: {
        model: { type: 'select', options: ['gpt-4', 'gpt-3.5-turbo', 'claude-3'] },
        temperature: { type: 'number', min: 0, max: 2, default: 0.7 },
        maxTokens: { type: 'number', min: 1, max: 4000, default: 1000 }
      },
      execute: async (inputs, properties) => {
        const response = await this.callLLM({
          model: properties.model,
          prompt: inputs.prompt,
          context: inputs.context,
          temperature: properties.temperature,
          maxTokens: properties.maxTokens
        });
        return { response: response.content };
      }
    });

    // 函数调用节点
    this.registerNodeType('function_call', {
      name: 'Function Call',
      category: 'tools',
      inputs: [
        { name: 'function_name', type: 'string' },
        { name: 'parameters', type: 'object' }
      ],
      outputs: [{ name: 'result', type: 'any' }],
      properties: {
        timeout: { type: 'number', default: 30000 }
      },
      execute: async (inputs, properties) => {
        const result = await this.executeFunctionCall(
          inputs.function_name,
          inputs.parameters,
          properties.timeout
        );
        return { result };
      }
    });

    // 条件判断节点
    this.registerNodeType('condition', {
      name: 'Condition',
      category: 'logic',
      inputs: [
        { name: 'value', type: 'any' },
        { name: 'condition', type: 'string' }
      ],
      outputs: [
        { name: 'true', type: 'any' },
        { name: 'false', type: 'any' }
      ],
      properties: {
        operator: { type: 'select', options: ['==', '!=', '>', '<', '>=', '<=', 'contains'] }
      },
      execute: async (inputs, properties) => {
        const result = this.evaluateCondition(
          inputs.value,
          properties.operator,
          inputs.condition
        );
        return result ? { true: inputs.value } : { false: inputs.value };
      }
    });

    // 自定义代码节点
    this.registerNodeType('custom_code', {
      name: 'Custom Code',
      category: 'advanced',
      inputs: [{ name: 'input', type: 'any' }],
      outputs: [{ name: 'output', type: 'any' }],
      properties: {
        code: { type: 'code', language: 'javascript' },
        timeout: { type: 'number', default: 5000 }
      },
      execute: async (inputs, properties) => {
        const result = await this.sandbox.execute(
          properties.code,
          inputs,
          properties.timeout
        );
        return { output: result };
      }
    });
  }

  addNode(nodeData) {
    const nodeType = this.nodeTypes.get(nodeData.type);
    if (!nodeType) {
      throw new Error(`Unknown node type: ${nodeData.type}`);
    }

    const node = {
      id: nodeData.id || this.generateNodeId(),
      type: nodeData.type,
      position: nodeData.position,
      properties: { ...nodeType.properties, ...nodeData.properties },
      definition: nodeType
    };

    this.nodes.set(node.id, node);
    return node;
  }

  addConnection(connection) {
    // 验证连接的有效性
    const sourceNode = this.nodes.get(connection.source.nodeId);
    const targetNode = this.nodes.get(connection.target.nodeId);

    if (!sourceNode || !targetNode) {
      throw new Error('Invalid connection: node not found');
    }

    // 检查类型兼容性
    const sourceOutput = sourceNode.definition.outputs.find(
      o => o.name === connection.source.outputName
    );
    const targetInput = targetNode.definition.inputs.find(
      i => i.name === connection.target.inputName
    );

    if (!this.isTypeCompatible(sourceOutput.type, targetInput.type)) {
      throw new Error('Type mismatch in connection');
    }

    this.connections.push(connection);
  }

  async executeWorkflow(startNodeId, initialData = {}) {
    const executionContext = {
      data: new Map(),
      visited: new Set(),
      results: new Map()
    };

    // 设置初始数据
    executionContext.data.set(startNodeId, initialData);

    // 执行工作流
    const result = await this.executeNode(startNodeId, executionContext);

    return {
      result,
      executionLog: Array.from(executionContext.results.entries())
    };
  }

  async executeNode(nodeId, context) {
    if (context.visited.has(nodeId)) {
      throw new Error(`Circular dependency detected at node ${nodeId}`);
    }

    context.visited.add(nodeId);

    const node = this.nodes.get(nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }

    // 收集输入数据
    const inputs = await this.collectNodeInputs(nodeId, context);

    // 执行节点
    const outputs = await node.definition.execute(inputs, node.properties);

    // 保存结果
    context.results.set(nodeId, { inputs, outputs });

    // 传播数据到连接的节点
    await this.propagateOutputs(nodeId, outputs, context);

    context.visited.delete(nodeId);

    return outputs;
  }

  async collectNodeInputs(nodeId, context) {
    const inputs = {};

    // 查找连接到此节点的输入
    const incomingConnections = this.connections.filter(
      conn => conn.target.nodeId === nodeId
    );

    for (const connection of incomingConnections) {
      const sourceData = context.data.get(connection.source.nodeId);
      if (sourceData && sourceData[connection.source.outputName] !== undefined) {
        inputs[connection.target.inputName] = sourceData[connection.source.outputName];
      }
    }

    return inputs;
  }

  generateNodeId() {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 代码沙箱实现
class CodeSandbox {
  constructor() {
    this.worker = null;
    this.initializeWorker();
  }

  initializeWorker() {
    const workerCode = `
      self.onmessage = function(e) {
        const { code, inputs, timeout } = e.data;

        try {
          // 创建安全的执行环境
          const safeGlobals = {
            console: {
              log: (...args) => self.postMessage({ type: 'log', data: args })
            },
            setTimeout: (fn, delay) => setTimeout(fn, Math.min(delay, 1000)),
            setInterval: () => { throw new Error('setInterval not allowed'); },
            fetch: () => { throw new Error('fetch not allowed'); },
            XMLHttpRequest: () => { throw new Error('XMLHttpRequest not allowed'); }
          };

          // 执行用户代码
          const func = new Function('inputs', 'globals', \`
            const { \${Object.keys(safeGlobals).join(', ')} } = globals;
            \${code}
          \`);

          const result = func(inputs, safeGlobals);

          self.postMessage({ type: 'result', data: result });
        } catch (error) {
          self.postMessage({ type: 'error', data: error.message });
        }
      };
    `;

    const blob = new Blob([workerCode], { type: 'application/javascript' });
    this.worker = new Worker(URL.createObjectURL(blob));
  }

  async execute(code, inputs, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.worker.terminate();
        this.initializeWorker();
        reject(new Error('Code execution timeout'));
      }, timeout);

      this.worker.onmessage = (e) => {
        const { type, data } = e.data;

        switch (type) {
          case 'result':
            clearTimeout(timeoutId);
            resolve(data);
            break;
          case 'error':
            clearTimeout(timeoutId);
            reject(new Error(data));
            break;
          case 'log':
            console.log('Sandbox log:', ...data);
            break;
        }
      };

      this.worker.postMessage({ code, inputs, timeout });
    });
  }
}

// 使用示例
const builder = new VisualAgentBuilder(document.getElementById('agent-builder'));

// 创建一个简单的工作流
const inputNode = builder.addNode({
  type: 'input',
  position: { x: 100, y: 100 },
  properties: { inputType: 'text', defaultValue: 'Hello, AI!' }
});

const llmNode = builder.addNode({
  type: 'llm',
  position: { x: 300, y: 100 },
  properties: { model: 'gpt-4', temperature: 0.7 }
});

// 连接节点
builder.addConnection({
  source: { nodeId: inputNode.id, outputName: 'data' },
  target: { nodeId: llmNode.id, inputName: 'prompt' }
});

// 执行工作流
const result = await builder.executeWorkflow(inputNode.id);
console.log('Workflow result:', result);
```

**技术要点**：
- 可视化节点编辑器设计
- 代码沙箱安全执行
- 工作流引擎实现
- 自定义节点扩展机制

**实践项目**：
- 开发可视化Agent构建平台
- 实现节点库和模板系统
- 构建Agent部署和监控工具

### 88. 端侧推理（WebLLM、Transformers.js、ONNX.js）
**掌握程度**：前端加载模型，本地推理，显示推理进度

**技术解释与实现方案**：
端侧推理技术允许在浏览器中直接运行大语言模型，无需依赖服务器端API。它通过WebAssembly、WebGL等技术实现高性能的模型推理，为用户提供更快的响应速度和更好的隐私保护。

**核心算法原理**：
- **模型量化**：通过INT8、INT4等量化技术减少模型大小
- **计算优化**：使用WebGL、WebGPU进行并行计算加速
- **内存管理**：优化模型加载和推理过程中的内存使用
- **流式推理**：支持流式输出，提升用户体验

**技术实现方案**：
```javascript
// WebLLM端侧推理实现
class WebLLMInference {
  constructor(config) {
    this.config = config;
    this.model = null;
    this.tokenizer = null;
    this.isLoaded = false;
    this.loadingProgress = 0;
  }

  async loadModel(modelName, progressCallback) {
    try {
      this.loadingProgress = 0;
      progressCallback?.(0, 'Initializing...');

      // 导入WebLLM
      const { CreateMLCEngine } = await import('@mlc-ai/web-llm');

      progressCallback?.(10, 'Loading model configuration...');

      // 创建推理引擎
      this.engine = await CreateMLCEngine(modelName, {
        initProgressCallback: (progress) => {
          this.loadingProgress = 10 + (progress.progress * 80);
          progressCallback?.(
            this.loadingProgress,
            `Loading ${progress.text}... (${Math.round(progress.progress * 100)}%)`
          );
        }
      });

      this.isLoaded = true;
      this.loadingProgress = 100;
      progressCallback?.(100, 'Model loaded successfully!');

    } catch (error) {
      console.error('Failed to load model:', error);
      throw new Error(`Model loading failed: ${error.message}`);
    }
  }

  async generateText(prompt, options = {}) {
    if (!this.isLoaded) {
      throw new Error('Model not loaded. Call loadModel() first.');
    }

    const {
      maxTokens = 256,
      temperature = 0.7,
      topP = 0.9,
      stream = false,
      onProgress = null
    } = options;

    if (stream) {
      return this.generateTextStream(prompt, {
        maxTokens,
        temperature,
        topP,
        onProgress
      });
    }

    const response = await this.engine.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      max_tokens: maxTokens,
      temperature,
      top_p: topP
    });

    return response.choices[0].message.content;
  }

  async *generateTextStream(prompt, options) {
    const {
      maxTokens = 256,
      temperature = 0.7,
      topP = 0.9,
      onProgress = null
    } = options;

    const stream = await this.engine.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      max_tokens: maxTokens,
      temperature,
      top_p: topP,
      stream: true
    });

    let generatedText = '';
    let tokenCount = 0;

    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta?.content || '';
      if (delta) {
        generatedText += delta;
        tokenCount++;

        onProgress?.({
          text: generatedText,
          tokenCount,
          isComplete: false
        });

        yield {
          text: delta,
          fullText: generatedText,
          tokenCount,
          isComplete: false
        };
      }
    }

    onProgress?.({
      text: generatedText,
      tokenCount,
      isComplete: true
    });

    yield {
      text: '',
      fullText: generatedText,
      tokenCount,
      isComplete: true
    };
  }

  getModelInfo() {
    if (!this.isLoaded) {
      return null;
    }

    return {
      name: this.config.modelName,
      loaded: this.isLoaded,
      memoryUsage: this.getMemoryUsage(),
      supportedFeatures: ['text-generation', 'chat', 'streaming']
    };
  }

  getMemoryUsage() {
    if (performance.memory) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      };
    }
    return null;
  }

  async unloadModel() {
    if (this.engine) {
      await this.engine.unload();
      this.engine = null;
    }
    this.isLoaded = false;
    this.loadingProgress = 0;
  }
}

// Transformers.js实现
class TransformersJSInference {
  constructor() {
    this.pipeline = null;
    this.isLoaded = false;
  }

  async loadModel(task, model, progressCallback) {
    try {
      const { pipeline } = await import('@xenova/transformers');

      progressCallback?.(0, 'Loading Transformers.js...');

      this.pipeline = await pipeline(task, model, {
        progress_callback: (progress) => {
          const percentage = Math.round(progress.progress * 100);
          progressCallback?.(percentage, `Loading ${progress.file}...`);
        }
      });

      this.isLoaded = true;
      progressCallback?.(100, 'Model loaded successfully!');

    } catch (error) {
      throw new Error(`Failed to load model: ${error.message}`);
    }
  }

  async generate(input, options = {}) {
    if (!this.isLoaded) {
      throw new Error('Model not loaded');
    }

    return await this.pipeline(input, options);
  }
}

// ONNX.js实现
class ONNXJSInference {
  constructor() {
    this.session = null;
    this.isLoaded = false;
  }

  async loadModel(modelUrl, progressCallback) {
    try {
      const ort = await import('onnxruntime-web');

      progressCallback?.(0, 'Loading ONNX model...');

      // 下载模型
      const response = await fetch(modelUrl);
      const modelData = await response.arrayBuffer();

      progressCallback?.(50, 'Creating inference session...');

      this.session = await ort.InferenceSession.create(modelData);

      this.isLoaded = true;
      progressCallback?.(100, 'ONNX model loaded successfully!');

    } catch (error) {
      throw new Error(`Failed to load ONNX model: ${error.message}`);
    }
  }

  async run(inputs) {
    if (!this.isLoaded) {
      throw new Error('Model not loaded');
    }

    const feeds = {};
    for (const [key, value] of Object.entries(inputs)) {
      feeds[key] = new ort.Tensor('float32', value.data, value.dims);
    }

    const results = await this.session.run(feeds);
    return results;
  }
}

// 统一的端侧推理管理器
class EdgeInferenceManager {
  constructor() {
    this.engines = new Map();
    this.activeEngine = null;
  }

  registerEngine(name, engine) {
    this.engines.set(name, engine);
  }

  async loadModel(engineName, modelConfig, progressCallback) {
    const engine = this.engines.get(engineName);
    if (!engine) {
      throw new Error(`Engine ${engineName} not found`);
    }

    await engine.loadModel(modelConfig, progressCallback);
    this.activeEngine = engine;

    return engine;
  }

  async generate(input, options = {}) {
    if (!this.activeEngine) {
      throw new Error('No active engine');
    }

    return await this.activeEngine.generate(input, options);
  }

  getEngineInfo() {
    if (!this.activeEngine) {
      return null;
    }

    return this.activeEngine.getModelInfo?.() || { loaded: true };
  }
}

// 使用示例
const inferenceManager = new EdgeInferenceManager();

// 注册不同的推理引擎
inferenceManager.registerEngine('webllm', new WebLLMInference());
inferenceManager.registerEngine('transformers', new TransformersJSInference());
inferenceManager.registerEngine('onnx', new ONNXJSInference());

// 加载模型
const progressDiv = document.getElementById('loading-progress');
const progressBar = document.getElementById('progress-bar');

await inferenceManager.loadModel('webllm', 'Llama-2-7b-chat-hf-q4f16_1',
  (progress, message) => {
    progressBar.style.width = `${progress}%`;
    progressDiv.textContent = message;
  }
);

// 生成文本
const response = await inferenceManager.generate(
  'What is the capital of France?',
  {
    maxTokens: 100,
    temperature: 0.7,
    stream: true,
    onProgress: (data) => {
      document.getElementById('output').textContent = data.text;
    }
  }
);
```

**技术要点**：
- 模型加载和内存管理
- WebAssembly和WebGL优化
- 流式推理实现
- 多引擎适配和管理

**实践项目**：
- 开发端侧推理框架
- 实现模型性能监控
- 构建离线AI应用

### 89. Agent 性能监控（推理延迟、Token 消耗、成功率）
**掌握程度**：前端实时图表展示各项指标

**技术解释与实现方案**：
Agent性能监控系统通过收集和分析Agent运行过程中的各项指标，帮助开发者优化Agent性能、控制成本、提升用户体验。它包括实时监控、历史分析、告警通知等功能。

**核心算法原理**：
- **指标收集**：实时收集推理延迟、Token消耗、成功率等关键指标
- **数据聚合**：按时间窗口聚合数据，计算平均值、百分位数等统计量
- **异常检测**：使用统计方法检测性能异常和趋势变化
- **可视化展示**：通过图表直观展示性能趋势和分布

**技术实现方案**：
```javascript
// Agent性能监控系统
class AgentPerformanceMonitor {
  constructor(config) {
    this.config = config;
    this.metrics = new Map();
    this.collectors = new Map();
    this.alerts = [];
    this.dashboard = null;

    this.initializeCollectors();
  }

  initializeCollectors() {
    // 延迟收集器
    this.collectors.set('latency', new LatencyCollector());

    // Token消耗收集器
    this.collectors.set('tokens', new TokenCollector());

    // 成功率收集器
    this.collectors.set('success_rate', new SuccessRateCollector());

    // 内存使用收集器
    this.collectors.set('memory', new MemoryCollector());

    // 错误收集器
    this.collectors.set('errors', new ErrorCollector());
  }

  startMonitoring(agentId) {
    const agent = this.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    // 包装Agent方法以收集指标
    this.wrapAgentMethods(agent);

    // 启动定时收集
    this.startPeriodicCollection(agentId);
  }

  wrapAgentMethods(agent) {
    const originalExecute = agent.execute.bind(agent);

    agent.execute = async (task) => {
      const startTime = performance.now();
      const startMemory = this.getMemoryUsage();

      try {
        const result = await originalExecute(task);

        // 记录成功指标
        this.recordMetric('latency', {
          agentId: agent.id,
          duration: performance.now() - startTime,
          task: task.type,
          timestamp: Date.now()
        });

        this.recordMetric('success_rate', {
          agentId: agent.id,
          success: true,
          task: task.type,
          timestamp: Date.now()
        });

        // 记录Token消耗（如果有的话）
        if (result.tokenUsage) {
          this.recordMetric('tokens', {
            agentId: agent.id,
            inputTokens: result.tokenUsage.input,
            outputTokens: result.tokenUsage.output,
            totalTokens: result.tokenUsage.total,
            task: task.type,
            timestamp: Date.now()
          });
        }

        return result;

      } catch (error) {
        // 记录失败指标
        this.recordMetric('latency', {
          agentId: agent.id,
          duration: performance.now() - startTime,
          task: task.type,
          error: true,
          timestamp: Date.now()
        });

        this.recordMetric('success_rate', {
          agentId: agent.id,
          success: false,
          task: task.type,
          error: error.message,
          timestamp: Date.now()
        });

        this.recordMetric('errors', {
          agentId: agent.id,
          error: error.message,
          task: task.type,
          timestamp: Date.now()
        });

        throw error;
      } finally {
        // 记录内存使用
        const endMemory = this.getMemoryUsage();
        this.recordMetric('memory', {
          agentId: agent.id,
          memoryDelta: endMemory - startMemory,
          currentMemory: endMemory,
          timestamp: Date.now()
        });
      }
    };
  }

  recordMetric(type, data) {
    const collector = this.collectors.get(type);
    if (collector) {
      collector.collect(data);
    }

    // 检查告警条件
    this.checkAlerts(type, data);
  }

  checkAlerts(type, data) {
    const alertRules = this.config.alerts?.[type] || [];

    for (const rule of alertRules) {
      if (this.evaluateAlertRule(rule, data)) {
        this.triggerAlert(rule, data);
      }
    }
  }

  evaluateAlertRule(rule, data) {
    switch (rule.condition) {
      case 'threshold':
        return data[rule.field] > rule.threshold;
      case 'rate':
        const recentData = this.getRecentData(rule.type, rule.timeWindow);
        const rate = this.calculateRate(recentData, rule.field);
        return rate > rule.threshold;
      default:
        return false;
    }
  }

  triggerAlert(rule, data) {
    const alert = {
      id: this.generateAlertId(),
      rule: rule.name,
      severity: rule.severity,
      message: rule.message,
      data,
      timestamp: Date.now()
    };

    this.alerts.push(alert);

    // 发送通知
    if (this.config.notifications) {
      this.sendNotification(alert);
    }
  }

  getMetrics(agentId, timeRange = '1h') {
    const metrics = {};

    for (const [type, collector] of this.collectors) {
      metrics[type] = collector.getMetrics(agentId, timeRange);
    }

    return metrics;
  }

  createDashboard(containerId) {
    this.dashboard = new PerformanceDashboard(containerId, this);
    return this.dashboard;
  }

  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize;
    }
    return 0;
  }
}

// 延迟收集器
class LatencyCollector {
  constructor() {
    this.data = [];
    this.maxDataPoints = 10000;
  }

  collect(data) {
    this.data.push(data);

    // 保持数据量在限制内
    if (this.data.length > this.maxDataPoints) {
      this.data = this.data.slice(-this.maxDataPoints);
    }
  }

  getMetrics(agentId, timeRange) {
    const filtered = this.filterByTimeRange(this.data, timeRange)
      .filter(d => d.agentId === agentId);

    if (filtered.length === 0) {
      return null;
    }

    const durations = filtered.map(d => d.duration);

    return {
      count: filtered.length,
      average: this.calculateAverage(durations),
      median: this.calculatePercentile(durations, 50),
      p95: this.calculatePercentile(durations, 95),
      p99: this.calculatePercentile(durations, 99),
      min: Math.min(...durations),
      max: Math.max(...durations),
      trend: this.calculateTrend(filtered)
    };
  }

  calculateAverage(values) {
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  calculatePercentile(values, percentile) {
    const sorted = values.slice().sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }

  calculateTrend(data) {
    if (data.length < 2) return 0;

    const recent = data.slice(-Math.min(100, data.length));
    const firstHalf = recent.slice(0, Math.floor(recent.length / 2));
    const secondHalf = recent.slice(Math.floor(recent.length / 2));

    const firstAvg = this.calculateAverage(firstHalf.map(d => d.duration));
    const secondAvg = this.calculateAverage(secondHalf.map(d => d.duration));

    return ((secondAvg - firstAvg) / firstAvg) * 100;
  }

  filterByTimeRange(data, timeRange) {
    const now = Date.now();
    const ranges = {
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000
    };

    const rangeMs = ranges[timeRange] || ranges['1h'];
    const cutoff = now - rangeMs;

    return data.filter(d => d.timestamp >= cutoff);
  }
}

// 性能仪表板
class PerformanceDashboard {
  constructor(containerId, monitor) {
    this.container = document.getElementById(containerId);
    this.monitor = monitor;
    this.charts = new Map();

    this.initializeDashboard();
    this.startRealTimeUpdates();
  }

  initializeDashboard() {
    this.container.innerHTML = `
      <div class="dashboard-header">
        <h2>Agent Performance Monitor</h2>
        <div class="time-range-selector">
          <select id="timeRange">
            <option value="5m">Last 5 minutes</option>
            <option value="15m">Last 15 minutes</option>
            <option value="1h" selected>Last hour</option>
            <option value="6h">Last 6 hours</option>
            <option value="24h">Last 24 hours</option>
          </select>
        </div>
      </div>
      <div class="dashboard-grid">
        <div class="chart-container">
          <h3>Response Latency</h3>
          <canvas id="latencyChart"></canvas>
        </div>
        <div class="chart-container">
          <h3>Token Consumption</h3>
          <canvas id="tokenChart"></canvas>
        </div>
        <div class="chart-container">
          <h3>Success Rate</h3>
          <canvas id="successChart"></canvas>
        </div>
        <div class="chart-container">
          <h3>Memory Usage</h3>
          <canvas id="memoryChart"></canvas>
        </div>
      </div>
      <div class="alerts-panel">
        <h3>Recent Alerts</h3>
        <div id="alertsList"></div>
      </div>
    `;

    this.initializeCharts();
  }

  initializeCharts() {
    // 使用Chart.js创建图表
    const Chart = window.Chart;

    // 延迟图表
    this.charts.set('latency', new Chart(
      document.getElementById('latencyChart').getContext('2d'),
      {
        type: 'line',
        data: {
          labels: [],
          datasets: [{
            label: 'Average Latency (ms)',
            data: [],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
          }]
        },
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      }
    ));

    // Token消耗图表
    this.charts.set('tokens', new Chart(
      document.getElementById('tokenChart').getContext('2d'),
      {
        type: 'bar',
        data: {
          labels: [],
          datasets: [{
            label: 'Tokens per Request',
            data: [],
            backgroundColor: 'rgba(54, 162, 235, 0.5)'
          }]
        },
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      }
    ));
  }

  startRealTimeUpdates() {
    setInterval(() => {
      this.updateCharts();
      this.updateAlerts();
    }, 5000); // 每5秒更新一次
  }

  updateCharts() {
    const timeRange = document.getElementById('timeRange').value;

    // 更新每个图表
    for (const [type, chart] of this.charts) {
      const metrics = this.monitor.getMetrics('all', timeRange);
      if (metrics[type]) {
        this.updateChart(chart, metrics[type]);
      }
    }
  }

  updateChart(chart, data) {
    // 更新图表数据
    chart.data.labels = data.labels || [];
    chart.data.datasets[0].data = data.values || [];
    chart.update();
  }

  updateAlerts() {
    const alertsList = document.getElementById('alertsList');
    const recentAlerts = this.monitor.alerts.slice(-10);

    alertsList.innerHTML = recentAlerts.map(alert => `
      <div class="alert alert-${alert.severity}">
        <span class="alert-time">${new Date(alert.timestamp).toLocaleTimeString()}</span>
        <span class="alert-message">${alert.message}</span>
      </div>
    `).join('');
  }
}

// 使用示例
const monitor = new AgentPerformanceMonitor({
  alerts: {
    latency: [{
      name: 'High Latency',
      condition: 'threshold',
      field: 'duration',
      threshold: 5000, // 5秒
      severity: 'warning',
      message: 'Agent response time is too high'
    }],
    success_rate: [{
      name: 'Low Success Rate',
      condition: 'rate',
      field: 'success',
      threshold: 0.95,
      timeWindow: '5m',
      severity: 'critical',
      message: 'Agent success rate dropped below 95%'
    }]
  }
});

// 开始监控Agent
monitor.startMonitoring('agent-001');

// 创建仪表板
const dashboard = monitor.createDashboard('dashboard-container');
```

**技术要点**：
- 实时指标收集和聚合
- 多维度性能分析
- 告警规则配置和触发
- 可视化图表展示

**实践项目**：
- 开发Agent监控平台
- 实现性能优化建议系统
- 构建成本分析工具

### 90. Agent 安全沙箱（代码执行隔离、权限控制）
**掌握程度**：前端配置沙箱策略，显示权限状态

**技术解释与实现方案**：
Agent安全沙箱通过隔离执行环境、权限控制、资源限制等机制，确保Agent在安全的环境中运行，防止恶意代码执行、数据泄露、系统攻击等安全风险。

**核心算法原理**：
- **执行隔离**：使用Web Worker、iframe等技术隔离代码执行环境
- **权限控制**：基于角色的访问控制（RBAC）限制Agent的操作权限
- **资源限制**：限制CPU、内存、网络等资源的使用
- **安全策略**：内容安全策略（CSP）防止XSS等攻击

**技术实现方案**：
```javascript
// Agent安全沙箱系统
class AgentSecuritySandbox {
  constructor(config) {
    this.config = config;
    this.sandboxes = new Map();
    this.policies = new Map();
    this.permissions = new Map();

    this.initializeDefaultPolicies();
  }

  initializeDefaultPolicies() {
    // 默认安全策略
    this.policies.set('default', {
      name: 'Default Policy',
      allowedAPIs: ['console.log', 'Math.*', 'Date.*'],
      blockedAPIs: ['eval', 'Function', 'XMLHttpRequest', 'fetch'],
      resourceLimits: {
        memory: 100 * 1024 * 1024, // 100MB
        executionTime: 30000, // 30秒
        networkRequests: 10
      },
      permissions: {
        fileSystem: false,
        network: false,
        localStorage: false,
        geolocation: false,
        camera: false,
        microphone: false
      }
    });

    // 受限策略
    this.policies.set('restricted', {
      name: 'Restricted Policy',
      allowedAPIs: ['console.log', 'Math.*'],
      blockedAPIs: ['*'], // 阻止所有未明确允许的API
      resourceLimits: {
        memory: 50 * 1024 * 1024, // 50MB
        executionTime: 10000, // 10秒
        networkRequests: 0
      },
      permissions: {
        fileSystem: false,
        network: false,
        localStorage: false,
        geolocation: false,
        camera: false,
        microphone: false
      }
    });

    // 开发者策略
    this.policies.set('developer', {
      name: 'Developer Policy',
      allowedAPIs: ['*'], // 允许大部分API
      blockedAPIs: ['eval', 'Function'],
      resourceLimits: {
        memory: 500 * 1024 * 1024, // 500MB
        executionTime: 120000, // 2分钟
        networkRequests: 100
      },
      permissions: {
        fileSystem: false,
        network: true,
        localStorage: true,
        geolocation: false,
        camera: false,
        microphone: false
      }
    });
  }

  async createSandbox(agentId, policyName = 'default') {
    const policy = this.policies.get(policyName);
    if (!policy) {
      throw new Error(`Policy ${policyName} not found`);
    }

    const sandbox = new SecureSandbox(agentId, policy);
    await sandbox.initialize();

    this.sandboxes.set(agentId, sandbox);
    return sandbox;
  }

  async executeInSandbox(agentId, code, context = {}) {
    const sandbox = this.sandboxes.get(agentId);
    if (!sandbox) {
      throw new Error(`Sandbox for agent ${agentId} not found`);
    }

    return await sandbox.execute(code, context);
  }

  getSandboxStatus(agentId) {
    const sandbox = this.sandboxes.get(agentId);
    if (!sandbox) {
      return null;
    }

    return sandbox.getStatus();
  }

  updatePermissions(agentId, permissions) {
    const sandbox = this.sandboxes.get(agentId);
    if (sandbox) {
      sandbox.updatePermissions(permissions);
    }
  }

  destroySandbox(agentId) {
    const sandbox = this.sandboxes.get(agentId);
    if (sandbox) {
      sandbox.destroy();
      this.sandboxes.delete(agentId);
    }
  }
}

// 安全沙箱实现
class SecureSandbox {
  constructor(agentId, policy) {
    this.agentId = agentId;
    this.policy = policy;
    this.worker = null;
    this.iframe = null;
    this.resourceUsage = {
      memory: 0,
      executionTime: 0,
      networkRequests: 0
    };
    this.isActive = false;
  }

  async initialize() {
    // 创建Web Worker作为主要执行环境
    await this.createWorker();

    // 创建iframe作为DOM操作环境（如果需要）
    if (this.policy.permissions.dom) {
      await this.createIframe();
    }

    this.isActive = true;
  }

  async createWorker() {
    const workerCode = this.generateWorkerCode();
    const blob = new Blob([workerCode], { type: 'application/javascript' });
    this.worker = new Worker(URL.createObjectURL(blob));

    // 设置消息处理
    this.worker.onmessage = (event) => {
      this.handleWorkerMessage(event);
    };

    this.worker.onerror = (error) => {
      console.error('Sandbox worker error:', error);
    };
  }

  generateWorkerCode() {
    const allowedAPIs = this.policy.allowedAPIs;
    const blockedAPIs = this.policy.blockedAPIs;

    return `
      // 安全沙箱Worker代码
      const originalConsole = console;
      const safeConsole = {
        log: (...args) => originalConsole.log('[Sandbox]', ...args),
        warn: (...args) => originalConsole.warn('[Sandbox]', ...args),
        error: (...args) => originalConsole.error('[Sandbox]', ...args)
      };

      // 创建安全的全局环境
      const safeGlobals = {
        console: safeConsole,
        Math: Math,
        Date: Date,
        JSON: JSON,
        parseInt: parseInt,
        parseFloat: parseFloat,
        isNaN: isNaN,
        isFinite: isFinite
      };

      // 阻止危险API
      const blockedAPIs = ${JSON.stringify(blockedAPIs)};
      const allowedAPIs = ${JSON.stringify(allowedAPIs)};

      function isAPIAllowed(apiName) {
        // 检查是否在阻止列表中
        for (const blocked of blockedAPIs) {
          if (blocked === '*' && !allowedAPIs.includes(apiName)) {
            return false;
          }
          if (apiName.match(new RegExp(blocked.replace('*', '.*')))) {
            return false;
          }
        }

        // 检查是否在允许列表中
        for (const allowed of allowedAPIs) {
          if (allowed === '*') return true;
          if (apiName.match(new RegExp(allowed.replace('*', '.*')))) {
            return true;
          }
        }

        return false;
      }

      // 执行用户代码
      self.onmessage = function(e) {
        const { code, context, executionId } = e.data;

        try {
          const startTime = performance.now();

          // 创建执行函数
          const func = new Function('context', 'globals', \`
            const { \${Object.keys(safeGlobals).join(', ')} } = globals;
            \${code}
          \`);

          // 执行代码
          const result = func(context, safeGlobals);

          const executionTime = performance.now() - startTime;

          self.postMessage({
            type: 'result',
            executionId,
            data: result,
            executionTime
          });

        } catch (error) {
          self.postMessage({
            type: 'error',
            executionId,
            error: {
              message: error.message,
              stack: error.stack
            }
          });
        }
      };
    `;
  }

  async execute(code, context = {}) {
    if (!this.isActive) {
      throw new Error('Sandbox not initialized');
    }

    // 检查资源限制
    this.checkResourceLimits();

    const executionId = this.generateExecutionId();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Execution timeout'));
      }, this.policy.resourceLimits.executionTime);

      const messageHandler = (event) => {
        if (event.data.executionId === executionId) {
          clearTimeout(timeout);
          this.worker.removeEventListener('message', messageHandler);

          if (event.data.type === 'result') {
            // 更新资源使用情况
            this.resourceUsage.executionTime += event.data.executionTime;
            resolve(event.data.data);
          } else if (event.data.type === 'error') {
            reject(new Error(event.data.error.message));
          }
        }
      };

      this.worker.addEventListener('message', messageHandler);

      // 发送执行请求
      this.worker.postMessage({
        code,
        context,
        executionId
      });
    });
  }

  checkResourceLimits() {
    const limits = this.policy.resourceLimits;

    // 检查内存使用
    if (performance.memory && performance.memory.usedJSHeapSize > limits.memory) {
      throw new Error('Memory limit exceeded');
    }

    // 检查执行时间
    if (this.resourceUsage.executionTime > limits.executionTime) {
      throw new Error('Execution time limit exceeded');
    }

    // 检查网络请求数量
    if (this.resourceUsage.networkRequests > limits.networkRequests) {
      throw new Error('Network request limit exceeded');
    }
  }

  getStatus() {
    return {
      agentId: this.agentId,
      policy: this.policy.name,
      isActive: this.isActive,
      resourceUsage: { ...this.resourceUsage },
      resourceLimits: { ...this.policy.resourceLimits },
      permissions: { ...this.policy.permissions }
    };
  }

  updatePermissions(newPermissions) {
    this.policy.permissions = { ...this.policy.permissions, ...newPermissions };
  }

  destroy() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    if (this.iframe) {
      this.iframe.remove();
      this.iframe = null;
    }

    this.isActive = false;
  }

  generateExecutionId() {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 权限管理界面
class PermissionManager {
  constructor(sandbox) {
    this.sandbox = sandbox;
    this.container = null;
  }

  render(containerId) {
    this.container = document.getElementById(containerId);
    const status = this.sandbox.getStatus();

    this.container.innerHTML = `
      <div class="permission-manager">
        <h3>Agent Security Settings</h3>

        <div class="policy-info">
          <h4>Current Policy: ${status.policy}</h4>
          <div class="status-indicator ${status.isActive ? 'active' : 'inactive'}">
            ${status.isActive ? 'Active' : 'Inactive'}
          </div>
        </div>

        <div class="permissions-grid">
          <h4>Permissions</h4>
          ${this.renderPermissions(status.permissions)}
        </div>

        <div class="resource-usage">
          <h4>Resource Usage</h4>
          ${this.renderResourceUsage(status.resourceUsage, status.resourceLimits)}
        </div>

        <div class="actions">
          <button id="updatePermissions">Update Permissions</button>
          <button id="resetSandbox">Reset Sandbox</button>
        </div>
      </div>
    `;

    this.bindEvents();
  }

  renderPermissions(permissions) {
    return Object.entries(permissions).map(([key, value]) => `
      <div class="permission-item">
        <label>
          <input type="checkbox"
                 data-permission="${key}"
                 ${value ? 'checked' : ''}>
          ${this.formatPermissionName(key)}
        </label>
      </div>
    `).join('');
  }

  renderResourceUsage(usage, limits) {
    return `
      <div class="resource-item">
        <label>Memory Usage:</label>
        <div class="progress-bar">
          <div class="progress" style="width: ${(usage.memory / limits.memory) * 100}%"></div>
        </div>
        <span>${Math.round(usage.memory / 1024 / 1024)}MB / ${Math.round(limits.memory / 1024 / 1024)}MB</span>
      </div>

      <div class="resource-item">
        <label>Execution Time:</label>
        <div class="progress-bar">
          <div class="progress" style="width: ${(usage.executionTime / limits.executionTime) * 100}%"></div>
        </div>
        <span>${Math.round(usage.executionTime)}ms / ${limits.executionTime}ms</span>
      </div>

      <div class="resource-item">
        <label>Network Requests:</label>
        <div class="progress-bar">
          <div class="progress" style="width: ${(usage.networkRequests / limits.networkRequests) * 100}%"></div>
        </div>
        <span>${usage.networkRequests} / ${limits.networkRequests}</span>
      </div>
    `;
  }

  bindEvents() {
    document.getElementById('updatePermissions').addEventListener('click', () => {
      this.updatePermissions();
    });

    document.getElementById('resetSandbox').addEventListener('click', () => {
      this.resetSandbox();
    });
  }

  updatePermissions() {
    const checkboxes = this.container.querySelectorAll('input[type="checkbox"]');
    const newPermissions = {};

    checkboxes.forEach(checkbox => {
      const permission = checkbox.dataset.permission;
      newPermissions[permission] = checkbox.checked;
    });

    this.sandbox.updatePermissions(newPermissions);
    this.render(this.container.id);
  }

  resetSandbox() {
    this.sandbox.destroy();
    // 重新初始化沙箱的逻辑
  }

  formatPermissionName(name) {
    return name.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }
}

// 使用示例
const securitySandbox = new AgentSecuritySandbox({
  defaultPolicy: 'default'
});

// 为Agent创建沙箱
const sandbox = await securitySandbox.createSandbox('agent-001', 'restricted');

// 在沙箱中执行代码
try {
  const result = await securitySandbox.executeInSandbox('agent-001', `
    const data = context.inputData;
    const processed = data.map(item => item * 2);
    return { result: processed };
  `, {
    inputData: [1, 2, 3, 4, 5]
  });

  console.log('Execution result:', result);
} catch (error) {
  console.error('Sandbox execution failed:', error);
}

// 创建权限管理界面
const permissionManager = new PermissionManager(sandbox);
permissionManager.render('permission-container');

// 监控沙箱状态
setInterval(() => {
  const status = securitySandbox.getSandboxStatus('agent-001');
  console.log('Sandbox status:', status);
}, 5000);
```

**技术要点**：
- Web Worker安全隔离
- 权限控制和资源限制
- 代码执行监控
- 安全策略配置

**实践项目**：
- 开发Agent安全管理平台
- 实现代码审计工具
- 构建安全策略模板库

## 🛠️ 技术栈推荐

### Agent开发框架
- **LangChain**：全功能Agent开发框架
- **AutoGen**：微软开源的多Agent协作框架
- **CrewAI**：专注于多Agent协作的框架

### Function Calling工具
- **OpenAI Functions**：OpenAI官方Function Calling API
- **MCP (Model Context Protocol)**：Anthropic的工具调用协议
- **LangChain Tools**：丰富的工具集成库

### 端侧推理引擎
- **WebLLM**：浏览器端LLM推理引擎
- **Transformers.js**：Hugging Face的JavaScript库
- **ONNX.js**：微软的跨平台推理引擎

### 可视化开发
- **React Flow**：节点编辑器组件库
- **X6**：AntV的图编辑引擎
- **Rete.js**：可视化编程框架

## 📈 学习路径建议

### 第1-2周：Function Calling基础
- **理论学习**：Function Calling协议和原理
- **实践项目**：实现基础的工具调用系统
- **工具熟悉**：OpenAI Functions API使用

### 第3-4周：多Agent协作
- **理论学习**：多Agent系统设计模式
- **实践项目**：构建简单的多Agent工作流
- **框架使用**：AutoGen或CrewAI实践

### 第5-6周：前端Agent SDK
- **理论学习**：前端Agent架构设计
- **实践项目**：开发前端Agent SDK
- **集成实践**：与现有应用集成

### 第7-8周：可视化构建器
- **理论学习**：低代码平台设计原理
- **实践项目**：开发可视化Agent构建器
- **高级功能**：自定义节点和模板系统

### 第9-10周：端侧推理
- **理论学习**：端侧推理技术和优化
- **实践项目**：实现浏览器端模型推理
- **性能优化**：模型量化和加速技术

### 第11-12周：监控和安全
- **理论学习**：Agent监控和安全机制
- **实践项目**：构建监控和安全系统
- **综合应用**：完整的Agent平台开发

## 🎯 评估标准

### 入门级（理解基本概念）
- 能使用现有框架创建简单Agent
- 理解Function Calling的基本原理
- 能配置基础的工具调用

### 熟练级（掌握核心技能）
- 能设计和实现多Agent协作系统
- 掌握前端Agent SDK的开发
- 能进行基本的性能监控

### 精通级（深入技术细节）
- 能开发可视化Agent构建平台
- 掌握端侧推理技术和优化
- 能设计完整的安全沙箱系统

### 大师级（引领技术发展）
- 能设计大规模Agent协作架构
- 推动Agent技术在业务中的创新应用
- 能指导团队进行Agent技术选型

## 📚 推荐学习资源

### 技术文档
- LangChain官方文档
- OpenAI Function Calling指南
- AutoGen使用教程

### 开源项目
- LangChain Agent示例
- AutoGen多Agent案例
- WebLLM端侧推理

### 学习平台
- LangChain Academy
- Microsoft AI School
- Hugging Face Course

---

**下一步**：建议从Function Calling开始，逐步掌握Agent开发的完整技术栈。
