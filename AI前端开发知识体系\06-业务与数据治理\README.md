# 业务与数据治理

## 📋 模块概述

业务与数据治理是AI应用落地的关键环节，涉及用户行为分析、数据安全、合规性管理等多个方面。本模块帮助前端开发者理解AI应用的业务价值实现和数据治理要求，确保应用的可持续发展。

## 🎯 学习目标

通过学习本模块，您将能够：
- 掌握AI应用的用户行为分析和数据埋点
- 理解数据安全和隐私保护的技术实现
- 熟练使用A/B测试和效果评估工具
- 了解AI应用的合规性要求和实现方案
- 具备构建完整数据治理体系的能力

## 📚 知识点列表

### 91. 用户行为埋点（对话轮次、满意度、点击热力图）
**掌握程度**：前端自动埋点，可视化配置埋点规则

**技术解释与实现方案**：
用户行为埋点是收集用户在AI应用中的交互数据的技术，通过分析对话轮次、满意度评分、点击热力图等指标，帮助产品团队优化用户体验和提升产品价值。

**核心算法原理**：
- **事件追踪**：自动捕获用户的点击、滚动、输入等行为事件
- **会话分析**：分析用户的对话流程和交互模式
- **热力图生成**：统计页面元素的点击频率和分布
- **满意度量化**：通过显式和隐式反馈评估用户满意度

**技术实现方案**：
```javascript
// 用户行为埋点系统
class UserBehaviorTracker {
  constructor(config) {
    this.config = config;
    this.sessionId = this.generateSessionId();
    this.userId = config.userId;
    this.events = [];
    this.conversationContext = {
      sessionStart: Date.now(),
      messageCount: 0,
      satisfactionScores: [],
      topics: []
    };
    
    this.initializeTracking();
  }
  
  initializeTracking() {
    // 自动埋点初始化
    this.setupAutoTracking();
    
    // 对话相关埋点
    this.setupConversationTracking();
    
    // 满意度埋点
    this.setupSatisfactionTracking();
    
    // 热力图埋点
    this.setupHeatmapTracking();
  }
  
  setupAutoTracking() {
    // 页面访问埋点
    this.track('page_view', {
      url: window.location.href,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    });
    
    // 点击事件埋点
    document.addEventListener('click', (event) => {
      const element = event.target;
      const elementInfo = this.getElementInfo(element);
      
      this.track('click', {
        element: elementInfo,
        coordinates: { x: event.clientX, y: event.clientY },
        timestamp: Date.now()
      });
    });
    
    // 滚动事件埋点
    let scrollTimeout;
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.track('scroll', {
          scrollTop: window.pageYOffset,
          scrollHeight: document.documentElement.scrollHeight,
          viewportHeight: window.innerHeight,
          scrollPercentage: (window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight)) * 100,
          timestamp: Date.now()
        });
      }, 100);
    });
    
    // 页面停留时间
    window.addEventListener('beforeunload', () => {
      const stayTime = Date.now() - this.conversationContext.sessionStart;
      this.track('page_leave', {
        stayTime,
        messageCount: this.conversationContext.messageCount,
        timestamp: Date.now()
      });
    });
  }
  
  setupConversationTracking() {
    // 监听对话事件
    this.onConversationStart = (data) => {
      this.conversationContext.sessionStart = Date.now();
      this.track('conversation_start', {
        conversationType: data.type,
        initialQuery: data.query,
        timestamp: Date.now()
      });
    };
    
    this.onMessageSent = (message) => {
      this.conversationContext.messageCount++;
      this.track('message_sent', {
        messageLength: message.length,
        messageType: this.classifyMessage(message),
        conversationTurn: this.conversationContext.messageCount,
        timestamp: Date.now()
      });
    };
    
    this.onMessageReceived = (response) => {
      this.track('message_received', {
        responseLength: response.length,
        responseTime: response.responseTime,
        conversationTurn: this.conversationContext.messageCount,
        timestamp: Date.now()
      });
    };
    
    this.onConversationEnd = () => {
      const duration = Date.now() - this.conversationContext.sessionStart;
      this.track('conversation_end', {
        duration,
        totalMessages: this.conversationContext.messageCount,
        averageSatisfaction: this.calculateAverageSatisfaction(),
        timestamp: Date.now()
      });
    };
  }
  
  setupSatisfactionTracking() {
    // 显式满意度收集
    this.collectSatisfaction = (score, feedback = '') => {
      this.conversationContext.satisfactionScores.push(score);
      this.track('satisfaction_rating', {
        score,
        feedback,
        conversationTurn: this.conversationContext.messageCount,
        timestamp: Date.now()
      });
    };
    
    // 隐式满意度推断
    this.inferSatisfaction = (behavior) => {
      let implicitScore = 3; // 默认中性
      
      // 基于行为推断满意度
      if (behavior.quickResponse && behavior.continueConversation) {
        implicitScore = 4; // 较满意
      } else if (behavior.longPause || behavior.repeatQuery) {
        implicitScore = 2; // 不太满意
      } else if (behavior.immediateExit) {
        implicitScore = 1; // 不满意
      }
      
      this.track('satisfaction_inferred', {
        implicitScore,
        behavior,
        timestamp: Date.now()
      });
    };
  }
  
  setupHeatmapTracking() {
    this.heatmapData = new Map();
    
    // 收集点击热力图数据
    document.addEventListener('click', (event) => {
      const x = Math.floor(event.clientX / 10) * 10; // 10px网格
      const y = Math.floor(event.clientY / 10) * 10;
      const key = `${x},${y}`;
      
      this.heatmapData.set(key, (this.heatmapData.get(key) || 0) + 1);
    });
    
    // 定期上报热力图数据
    setInterval(() => {
      if (this.heatmapData.size > 0) {
        this.track('heatmap_data', {
          data: Object.fromEntries(this.heatmapData),
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          },
          timestamp: Date.now()
        });
        this.heatmapData.clear();
      }
    }, 30000); // 每30秒上报一次
  }
  
  track(eventName, data) {
    const event = {
      eventName,
      sessionId: this.sessionId,
      userId: this.userId,
      data,
      timestamp: Date.now()
    };
    
    this.events.push(event);
    
    // 发送到分析服务
    this.sendToAnalytics(event);
    
    // 本地存储（离线支持）
    this.storeLocally(event);
  }
  
  async sendToAnalytics(event) {
    try {
      await fetch(this.config.analyticsEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.warn('Failed to send analytics event:', error);
      // 失败时存储到本地队列
      this.storeFailedEvent(event);
    }
  }
  
  storeLocally(event) {
    const stored = JSON.parse(localStorage.getItem('analytics_events') || '[]');
    stored.push(event);
    
    // 限制本地存储大小
    if (stored.length > 1000) {
      stored.splice(0, stored.length - 1000);
    }
    
    localStorage.setItem('analytics_events', JSON.stringify(stored));
  }
  
  getElementInfo(element) {
    return {
      tagName: element.tagName,
      id: element.id,
      className: element.className,
      textContent: element.textContent?.substring(0, 100),
      attributes: this.getRelevantAttributes(element)
    };
  }
  
  getRelevantAttributes(element) {
    const relevantAttrs = ['data-track', 'data-component', 'role', 'aria-label'];
    const attrs = {};
    
    relevantAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        attrs[attr] = element.getAttribute(attr);
      }
    });
    
    return attrs;
  }
  
  classifyMessage(message) {
    // 简单的消息分类逻辑
    if (message.includes('?')) return 'question';
    if (message.includes('谢谢') || message.includes('thank')) return 'gratitude';
    if (message.length < 10) return 'short';
    if (message.length > 100) return 'long';
    return 'normal';
  }
  
  calculateAverageSatisfaction() {
    const scores = this.conversationContext.satisfactionScores;
    if (scores.length === 0) return null;
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }
  
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 可视化埋点配置器
class TrackingConfigManager {
  constructor() {
    this.rules = new Map();
    this.activeRules = new Set();
  }
  
  addRule(name, rule) {
    this.rules.set(name, rule);
  }
  
  activateRule(name) {
    const rule = this.rules.get(name);
    if (!rule) return;
    
    this.activeRules.add(name);
    this.applyRule(rule);
  }
  
  applyRule(rule) {
    switch (rule.type) {
      case 'click_tracking':
        this.setupClickTracking(rule);
        break;
      case 'form_tracking':
        this.setupFormTracking(rule);
        break;
      case 'scroll_tracking':
        this.setupScrollTracking(rule);
        break;
      case 'time_tracking':
        this.setupTimeTracking(rule);
        break;
    }
  }
  
  setupClickTracking(rule) {
    const elements = document.querySelectorAll(rule.selector);
    elements.forEach(element => {
      element.addEventListener('click', (event) => {
        if (rule.condition && !rule.condition(element, event)) {
          return;
        }
        
        const data = rule.dataExtractor ? rule.dataExtractor(element, event) : {};
        tracker.track(rule.eventName, data);
      });
    });
  }
  
  renderConfigUI(containerId) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
      <div class="tracking-config">
        <h3>埋点规则配置</h3>
        
        <div class="rule-list">
          ${Array.from(this.rules.entries()).map(([name, rule]) => `
            <div class="rule-item">
              <label>
                <input type="checkbox" 
                       data-rule="${name}" 
                       ${this.activeRules.has(name) ? 'checked' : ''}>
                ${rule.name || name}
              </label>
              <span class="rule-type">${rule.type}</span>
            </div>
          `).join('')}
        </div>
        
        <div class="add-rule">
          <h4>添加新规则</h4>
          <form id="ruleForm">
            <input type="text" name="name" placeholder="规则名称" required>
            <select name="type" required>
              <option value="click_tracking">点击追踪</option>
              <option value="form_tracking">表单追踪</option>
              <option value="scroll_tracking">滚动追踪</option>
              <option value="time_tracking">时间追踪</option>
            </select>
            <input type="text" name="selector" placeholder="CSS选择器" required>
            <input type="text" name="eventName" placeholder="事件名称" required>
            <button type="submit">添加规则</button>
          </form>
        </div>
      </div>
    `;
    
    this.bindConfigEvents(container);
  }
  
  bindConfigEvents(container) {
    // 规则开关
    container.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
      checkbox.addEventListener('change', (event) => {
        const ruleName = event.target.dataset.rule;
        if (event.target.checked) {
          this.activateRule(ruleName);
        } else {
          this.deactivateRule(ruleName);
        }
      });
    });
    
    // 添加新规则
    container.querySelector('#ruleForm').addEventListener('submit', (event) => {
      event.preventDefault();
      const formData = new FormData(event.target);
      const rule = {
        name: formData.get('name'),
        type: formData.get('type'),
        selector: formData.get('selector'),
        eventName: formData.get('eventName')
      };
      
      this.addRule(rule.name, rule);
      this.renderConfigUI(container.parentElement.id);
    });
  }
}

// 使用示例
const tracker = new UserBehaviorTracker({
  userId: 'user_123',
  analyticsEndpoint: 'https://api.analytics.com/events',
  apiKey: 'your-api-key'
});

// 配置对话追踪
tracker.onConversationStart({ type: 'chat', query: 'Hello' });
tracker.onMessageSent('How can I help you today?');
tracker.onMessageReceived({ 
  content: 'I can help you with...', 
  responseTime: 1200 
});

// 收集满意度
tracker.collectSatisfaction(4, 'Very helpful response');

// 配置可视化埋点
const configManager = new TrackingConfigManager();

// 添加预定义规则
configManager.addRule('chat_button_click', {
  name: '聊天按钮点击',
  type: 'click_tracking',
  selector: '.chat-button',
  eventName: 'chat_button_clicked',
  dataExtractor: (element, event) => ({
    buttonText: element.textContent,
    position: { x: event.clientX, y: event.clientY }
  })
});

configManager.renderConfigUI('tracking-config-container');
```

**技术要点**：
- 自动化事件捕获和数据收集
- 对话流程和用户行为分析
- 可视化配置和规则管理
- 离线数据存储和同步

**实践项目**：
- 开发用户行为分析平台
- 实现实时数据可视化
- 构建埋点规则管理系统

### 92. 业务流程自动化
**掌握程度**：工作流引擎，业务规则配置，流程监控

**技术解释与实现方案**：
业务流程自动化是通过技术手段将重复性的业务流程进行自动化处理，提高效率并减少人为错误。在AI应用中，这包括用户请求处理、数据审核、内容生成等流程的自动化。

**核心算法原理**：
- **工作流引擎**：基于状态机的流程执行引擎
- **规则引擎**：可配置的业务规则判断系统
- **事件驱动**：基于事件的流程触发和流转
- **异常处理**：自动化流程中的错误恢复机制

**技术实现方案**：
```javascript
// 业务流程自动化引擎
class WorkflowEngine {
  constructor() {
    this.workflows = new Map();
    this.instances = new Map();
    this.ruleEngine = new RuleEngine();
    this.eventBus = new EventBus();
    this.taskQueue = new TaskQueue();

    this.setupEventHandlers();
  }

  // 定义工作流
  defineWorkflow(name, definition) {
    const workflow = new Workflow(name, definition);
    this.workflows.set(name, workflow);
    return workflow;
  }

  // 启动工作流实例
  async startWorkflow(workflowName, initialData = {}) {
    const workflow = this.workflows.get(workflowName);
    if (!workflow) {
      throw new Error(`Workflow ${workflowName} not found`);
    }

    const instance = new WorkflowInstance(workflow, initialData);
    this.instances.set(instance.id, instance);

    // 开始执行
    await this.executeInstance(instance);

    return instance;
  }

  async executeInstance(instance) {
    try {
      while (!instance.isCompleted() && !instance.isError()) {
        const currentStep = instance.getCurrentStep();

        if (!currentStep) {
          instance.complete();
          break;
        }

        // 执行当前步骤
        await this.executeStep(instance, currentStep);

        // 检查是否需要等待外部事件
        if (currentStep.waitForEvent) {
          break; // 暂停执行，等待事件
        }

        // 移动到下一步
        instance.moveToNext();
      }

      if (instance.isCompleted()) {
        this.eventBus.emit('workflow.completed', {
          instanceId: instance.id,
          workflowName: instance.workflow.name,
          result: instance.data
        });
      }
    } catch (error) {
      instance.setError(error);
      this.handleWorkflowError(instance, error);
    }
  }

  async executeStep(instance, step) {
    const context = {
      data: instance.data,
      instance: instance,
      step: step
    };

    switch (step.type) {
      case 'task':
        await this.executeTask(context, step);
        break;
      case 'decision':
        await this.executeDecision(context, step);
        break;
      case 'parallel':
        await this.executeParallel(context, step);
        break;
      case 'subprocess':
        await this.executeSubprocess(context, step);
        break;
      case 'wait':
        await this.executeWait(context, step);
        break;
      default:
        throw new Error(`Unknown step type: ${step.type}`);
    }
  }

  async executeTask(context, step) {
    const { data, instance } = context;

    // 执行任务处理器
    if (step.handler) {
      const result = await step.handler(data, context);

      // 更新实例数据
      if (result && typeof result === 'object') {
        Object.assign(instance.data, result);
      }
    }

    // 执行自动化任务
    if (step.automation) {
      await this.executeAutomation(context, step.automation);
    }

    // 记录执行日志
    instance.addLog({
      step: step.name,
      timestamp: Date.now(),
      status: 'completed',
      data: step.logData ? step.logData(data) : null
    });
  }

  async executeDecision(context, step) {
    const { data } = context;

    // 使用规则引擎进行决策
    const decision = await this.ruleEngine.evaluate(step.rules, data);

    // 根据决策结果选择下一步
    const nextStep = step.branches[decision] || step.branches.default;
    if (nextStep) {
      context.instance.setNextStep(nextStep);
    }

    context.instance.addLog({
      step: step.name,
      timestamp: Date.now(),
      status: 'completed',
      decision: decision,
      nextStep: nextStep
    });
  }

  async executeParallel(context, step) {
    const { data, instance } = context;

    // 并行执行多个分支
    const promises = step.branches.map(async (branch) => {
      const branchInstance = instance.createBranch(branch);
      return this.executeInstance(branchInstance);
    });

    // 等待所有分支完成
    const results = await Promise.allSettled(promises);

    // 合并结果
    const mergedData = this.mergeParallelResults(results, step.mergeStrategy);
    Object.assign(instance.data, mergedData);

    instance.addLog({
      step: step.name,
      timestamp: Date.now(),
      status: 'completed',
      branches: results.length,
      mergeStrategy: step.mergeStrategy
    });
  }

  async executeAutomation(context, automation) {
    switch (automation.type) {
      case 'api_call':
        return await this.executeApiCall(context, automation);
      case 'data_processing':
        return await this.executeDataProcessing(context, automation);
      case 'notification':
        return await this.executeNotification(context, automation);
      case 'ai_processing':
        return await this.executeAiProcessing(context, automation);
    }
  }

  async executeApiCall(context, automation) {
    const { data } = context;
    const { url, method, headers, body } = automation.config;

    // 模板替换
    const processedUrl = this.processTemplate(url, data);
    const processedBody = this.processTemplate(body, data);

    const response = await fetch(processedUrl, {
      method: method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: processedBody ? JSON.stringify(processedBody) : undefined
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  }

  async executeDataProcessing(context, automation) {
    const { data } = context;
    const { processor, config } = automation;

    switch (processor) {
      case 'filter':
        return this.filterData(data, config);
      case 'transform':
        return this.transformData(data, config);
      case 'aggregate':
        return this.aggregateData(data, config);
      case 'validate':
        return this.validateData(data, config);
    }
  }

  async executeNotification(context, automation) {
    const { data } = context;
    const { type, recipients, template } = automation.config;

    const message = this.processTemplate(template, data);

    switch (type) {
      case 'email':
        await this.sendEmail(recipients, message);
        break;
      case 'sms':
        await this.sendSms(recipients, message);
        break;
      case 'webhook':
        await this.sendWebhook(recipients, message);
        break;
      case 'push':
        await this.sendPushNotification(recipients, message);
        break;
    }
  }

  async executeAiProcessing(context, automation) {
    const { data } = context;
    const { model, prompt, config } = automation;

    const processedPrompt = this.processTemplate(prompt, data);

    const aiResponse = await this.callAiModel(model, processedPrompt, config);

    return {
      aiResponse: aiResponse,
      processedAt: Date.now()
    };
  }

  setupEventHandlers() {
    // 处理外部事件
    this.eventBus.on('external.event', (event) => {
      const { instanceId, eventType, data } = event;
      const instance = this.instances.get(instanceId);

      if (instance && instance.isWaitingForEvent(eventType)) {
        // 恢复工作流执行
        instance.receiveEvent(eventType, data);
        this.executeInstance(instance);
      }
    });

    // 处理定时任务
    this.eventBus.on('timer.expired', (event) => {
      const { instanceId } = event;
      const instance = this.instances.get(instanceId);

      if (instance && instance.isWaitingForTimer()) {
        instance.resumeFromTimer();
        this.executeInstance(instance);
      }
    });
  }

  processTemplate(template, data) {
    if (typeof template !== 'string') return template;

    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match;
    });
  }

  handleWorkflowError(instance, error) {
    instance.addLog({
      step: 'error',
      timestamp: Date.now(),
      status: 'error',
      error: error.message,
      stack: error.stack
    });

    this.eventBus.emit('workflow.error', {
      instanceId: instance.id,
      workflowName: instance.workflow.name,
      error: error
    });

    // 执行错误处理流程
    if (instance.workflow.errorHandler) {
      this.executeErrorHandler(instance, error);
    }
  }
}

// 规则引擎
class RuleEngine {
  constructor() {
    this.rules = new Map();
    this.operators = {
      '==': (a, b) => a == b,
      '===': (a, b) => a === b,
      '!=': (a, b) => a != b,
      '!==': (a, b) => a !== b,
      '>': (a, b) => a > b,
      '>=': (a, b) => a >= b,
      '<': (a, b) => a < b,
      '<=': (a, b) => a <= b,
      'contains': (a, b) => a && a.includes && a.includes(b),
      'startsWith': (a, b) => a && a.startsWith && a.startsWith(b),
      'endsWith': (a, b) => a && a.endsWith && a.endsWith(b),
      'matches': (a, b) => a && new RegExp(b).test(a)
    };
  }

  async evaluate(rules, data) {
    for (const rule of rules) {
      if (await this.evaluateRule(rule, data)) {
        return rule.result;
      }
    }

    return 'default';
  }

  async evaluateRule(rule, data) {
    if (rule.conditions) {
      return await this.evaluateConditions(rule.conditions, data);
    }

    if (rule.function) {
      return await rule.function(data);
    }

    return false;
  }

  async evaluateConditions(conditions, data) {
    const { operator = 'and', rules } = conditions;

    const results = await Promise.all(
      rules.map(rule => this.evaluateSingleCondition(rule, data))
    );

    if (operator === 'and') {
      return results.every(result => result);
    } else if (operator === 'or') {
      return results.some(result => result);
    }

    return false;
  }

  async evaluateSingleCondition(condition, data) {
    const { field, operator, value } = condition;
    const fieldValue = this.getFieldValue(data, field);
    const operatorFn = this.operators[operator];

    if (!operatorFn) {
      throw new Error(`Unknown operator: ${operator}`);
    }

    return operatorFn(fieldValue, value);
  }

  getFieldValue(data, field) {
    return field.split('.').reduce((obj, key) => obj && obj[key], data);
  }
}

// 工作流定义示例
const aiContentWorkflow = workflowEngine.defineWorkflow('ai_content_processing', {
  name: 'AI内容处理流程',
  description: '自动化处理用户提交的内容请求',
  steps: [
    {
      name: 'validate_input',
      type: 'task',
      handler: async (data) => {
        // 输入验证
        if (!data.content || data.content.length < 10) {
          throw new Error('内容长度不足');
        }
        return { validated: true };
      }
    },
    {
      name: 'content_analysis',
      type: 'decision',
      rules: [
        {
          conditions: {
            operator: 'and',
            rules: [
              { field: 'content', operator: 'contains', value: '敏感词' }
            ]
          },
          result: 'reject'
        },
        {
          conditions: {
            operator: 'and',
            rules: [
              { field: 'content.length', operator: '>', value: 1000 }
            ]
          },
          result: 'long_content'
        }
      ],
      branches: {
        reject: 'send_rejection',
        long_content: 'split_content',
        default: 'ai_processing'
      }
    },
    {
      name: 'ai_processing',
      type: 'task',
      automation: {
        type: 'ai_processing',
        model: 'gpt-4',
        prompt: '请处理以下内容：{{content}}',
        config: {
          temperature: 0.7,
          maxTokens: 2000
        }
      }
    },
    {
      name: 'quality_check',
      type: 'task',
      handler: async (data) => {
        // 质量检查逻辑
        const quality = await checkContentQuality(data.aiResponse);
        return { quality };
      }
    },
    {
      name: 'send_result',
      type: 'task',
      automation: {
        type: 'notification',
        config: {
          type: 'webhook',
          recipients: ['{{callbackUrl}}'],
          template: {
            status: 'completed',
            result: '{{aiResponse}}',
            quality: '{{quality}}'
          }
        }
      }
    }
  ],
  errorHandler: {
    steps: [
      {
        name: 'log_error',
        type: 'task',
        automation: {
          type: 'notification',
          config: {
            type: 'email',
            recipients: ['<EMAIL>'],
            template: '工作流执行失败：{{error}}'
          }
        }
      }
    ]
  }
});

// 使用示例
async function processUserContent(content, callbackUrl) {
  const instance = await workflowEngine.startWorkflow('ai_content_processing', {
    content: content,
    callbackUrl: callbackUrl,
    userId: 'user_123',
    timestamp: Date.now()
  });

  return instance.id;
}
```

**技术要点**：
- 可视化工作流设计和配置
- 基于规则的自动化决策
- 异步任务处理和监控
- 错误处理和恢复机制

**实践项目**：
- 构建工作流管理平台
- 实现业务规则配置系统
- 开发流程监控仪表板

### 93. 数据质量管理
**掌握程度**：数据清洗，质量监控，异常检测

**技术解释与实现方案**：
数据质量管理是确保AI应用中数据准确性、完整性、一致性和及时性的关键技术。通过自动化的数据清洗、质量监控和异常检测，保障AI模型的输入数据质量。

**核心算法原理**：
- **数据清洗**：去除重复、纠正错误、填补缺失值
- **质量评估**：多维度数据质量指标计算
- **异常检测**：基于统计和机器学习的异常识别
- **数据血缘**：追踪数据来源和处理过程

**技术实现方案**：
```javascript
// 数据质量管理系统
class DataQualityManager {
  constructor(config = {}) {
    this.config = {
      qualityThresholds: {
        completeness: 0.95,
        accuracy: 0.98,
        consistency: 0.99,
        timeliness: 0.90
      },
      anomalyDetection: {
        enabled: true,
        sensitivity: 0.05,
        methods: ['statistical', 'isolation_forest', 'clustering']
      },
      ...config
    };

    this.qualityMetrics = new Map();
    this.cleaningRules = new Map();
    this.anomalyDetectors = new Map();
    this.dataLineage = new Map();

    this.initializeDetectors();
  }

  // 数据质量评估
  async assessDataQuality(dataset, schema) {
    const assessment = {
      timestamp: Date.now(),
      datasetId: dataset.id,
      totalRecords: dataset.records.length,
      metrics: {},
      issues: [],
      score: 0
    };

    // 完整性检查
    assessment.metrics.completeness = await this.checkCompleteness(dataset, schema);

    // 准确性检查
    assessment.metrics.accuracy = await this.checkAccuracy(dataset, schema);

    // 一致性检查
    assessment.metrics.consistency = await this.checkConsistency(dataset, schema);

    // 及时性检查
    assessment.metrics.timeliness = await this.checkTimeliness(dataset, schema);

    // 唯一性检查
    assessment.metrics.uniqueness = await this.checkUniqueness(dataset, schema);

    // 有效性检查
    assessment.metrics.validity = await this.checkValidity(dataset, schema);

    // 计算综合质量分数
    assessment.score = this.calculateQualityScore(assessment.metrics);

    // 生成质量报告
    assessment.report = this.generateQualityReport(assessment);

    return assessment;
  }

  async checkCompleteness(dataset, schema) {
    const completeness = {
      overall: 0,
      byField: {},
      missingValues: 0,
      totalValues: 0
    };

    const requiredFields = schema.fields.filter(field => field.required);

    for (const field of requiredFields) {
      let missingCount = 0;

      for (const record of dataset.records) {
        const value = record[field.name];
        if (value === null || value === undefined || value === '') {
          missingCount++;
          completeness.missingValues++;
        }
        completeness.totalValues++;
      }

      completeness.byField[field.name] = {
        missing: missingCount,
        total: dataset.records.length,
        rate: 1 - (missingCount / dataset.records.length)
      };
    }

    completeness.overall = 1 - (completeness.missingValues / completeness.totalValues);

    return completeness;
  }

  async checkAccuracy(dataset, schema) {
    const accuracy = {
      overall: 0,
      byField: {},
      errors: []
    };

    let totalChecks = 0;
    let passedChecks = 0;

    for (const field of schema.fields) {
      const fieldAccuracy = {
        checks: 0,
        passed: 0,
        errors: []
      };

      for (const record of dataset.records) {
        const value = record[field.name];
        if (value === null || value === undefined) continue;

        totalChecks++;
        fieldAccuracy.checks++;

        // 数据类型检查
        if (this.validateDataType(value, field.type)) {
          passedChecks++;
          fieldAccuracy.passed++;
        } else {
          const error = {
            recordId: record.id,
            field: field.name,
            value: value,
            expectedType: field.type,
            actualType: typeof value
          };
          fieldAccuracy.errors.push(error);
          accuracy.errors.push(error);
        }

        // 格式检查
        if (field.format && !this.validateFormat(value, field.format)) {
          const error = {
            recordId: record.id,
            field: field.name,
            value: value,
            expectedFormat: field.format,
            type: 'format_error'
          };
          fieldAccuracy.errors.push(error);
          accuracy.errors.push(error);
        }

        // 范围检查
        if (field.range && !this.validateRange(value, field.range)) {
          const error = {
            recordId: record.id,
            field: field.name,
            value: value,
            expectedRange: field.range,
            type: 'range_error'
          };
          fieldAccuracy.errors.push(error);
          accuracy.errors.push(error);
        }
      }

      fieldAccuracy.rate = fieldAccuracy.checks > 0 ?
        fieldAccuracy.passed / fieldAccuracy.checks : 1;
      accuracy.byField[field.name] = fieldAccuracy;
    }

    accuracy.overall = totalChecks > 0 ? passedChecks / totalChecks : 1;

    return accuracy;
  }

  async checkConsistency(dataset, schema) {
    const consistency = {
      overall: 0,
      crossField: {},
      temporal: {},
      referential: {},
      issues: []
    };

    // 跨字段一致性检查
    for (const rule of schema.consistencyRules || []) {
      const result = await this.checkConsistencyRule(dataset, rule);
      consistency.crossField[rule.name] = result;

      if (result.violations.length > 0) {
        consistency.issues.push(...result.violations);
      }
    }

    // 时间一致性检查
    if (schema.temporalFields) {
      consistency.temporal = await this.checkTemporalConsistency(dataset, schema.temporalFields);
    }

    // 引用一致性检查
    if (schema.referentialConstraints) {
      consistency.referential = await this.checkReferentialConsistency(dataset, schema.referentialConstraints);
    }

    // 计算总体一致性分数
    const totalIssues = consistency.issues.length;
    const totalRecords = dataset.records.length;
    consistency.overall = totalRecords > 0 ? 1 - (totalIssues / totalRecords) : 1;

    return consistency;
  }

  async checkTimeliness(dataset, schema) {
    const timeliness = {
      overall: 0,
      byField: {},
      latency: {},
      freshness: {}
    };

    const now = Date.now();

    for (const field of schema.fields.filter(f => f.temporal)) {
      const fieldTimeliness = {
        records: 0,
        onTime: 0,
        late: 0,
        averageLatency: 0,
        maxLatency: 0
      };

      let totalLatency = 0;

      for (const record of dataset.records) {
        const timestamp = new Date(record[field.name]).getTime();
        const expectedTime = record[field.expectedTime] ?
          new Date(record[field.expectedTime]).getTime() : now;

        const latency = Math.abs(timestamp - expectedTime);
        totalLatency += latency;
        fieldTimeliness.records++;

        if (latency <= field.timelinessThreshold) {
          fieldTimeliness.onTime++;
        } else {
          fieldTimeliness.late++;
        }

        fieldTimeliness.maxLatency = Math.max(fieldTimeliness.maxLatency, latency);
      }

      fieldTimeliness.averageLatency = fieldTimeliness.records > 0 ?
        totalLatency / fieldTimeliness.records : 0;
      fieldTimeliness.rate = fieldTimeliness.records > 0 ?
        fieldTimeliness.onTime / fieldTimeliness.records : 1;

      timeliness.byField[field.name] = fieldTimeliness;
    }

    // 计算总体及时性
    const fieldRates = Object.values(timeliness.byField).map(f => f.rate);
    timeliness.overall = fieldRates.length > 0 ?
      fieldRates.reduce((sum, rate) => sum + rate, 0) / fieldRates.length : 1;

    return timeliness;
  }

  // 数据清洗
  async cleanData(dataset, cleaningRules) {
    const cleanedDataset = {
      ...dataset,
      records: [...dataset.records],
      cleaningLog: []
    };

    for (const rule of cleaningRules) {
      const result = await this.applyCleaningRule(cleanedDataset, rule);
      cleanedDataset.cleaningLog.push(result);
    }

    return cleanedDataset;
  }

  async applyCleaningRule(dataset, rule) {
    const result = {
      ruleName: rule.name,
      type: rule.type,
      recordsProcessed: 0,
      recordsModified: 0,
      recordsRemoved: 0,
      changes: []
    };

    switch (rule.type) {
      case 'remove_duplicates':
        return await this.removeDuplicates(dataset, rule, result);
      case 'fill_missing':
        return await this.fillMissingValues(dataset, rule, result);
      case 'standardize_format':
        return await this.standardizeFormat(dataset, rule, result);
      case 'remove_outliers':
        return await this.removeOutliers(dataset, rule, result);
      case 'normalize_values':
        return await this.normalizeValues(dataset, rule, result);
      default:
        throw new Error(`Unknown cleaning rule type: ${rule.type}`);
    }
  }

  async removeDuplicates(dataset, rule, result) {
    const seen = new Set();
    const duplicateIndices = [];

    for (let i = 0; i < dataset.records.length; i++) {
      const record = dataset.records[i];
      const key = rule.keyFields.map(field => record[field]).join('|');

      result.recordsProcessed++;

      if (seen.has(key)) {
        duplicateIndices.push(i);
        result.recordsRemoved++;
        result.changes.push({
          recordId: record.id,
          action: 'removed',
          reason: 'duplicate',
          key: key
        });
      } else {
        seen.add(key);
      }
    }

    // 移除重复记录
    for (let i = duplicateIndices.length - 1; i >= 0; i--) {
      dataset.records.splice(duplicateIndices[i], 1);
    }

    return result;
  }

  async fillMissingValues(dataset, rule, result) {
    for (const record of dataset.records) {
      result.recordsProcessed++;

      const value = record[rule.field];
      if (value === null || value === undefined || value === '') {
        let fillValue;

        switch (rule.strategy) {
          case 'default':
            fillValue = rule.defaultValue;
            break;
          case 'mean':
            fillValue = await this.calculateMean(dataset, rule.field);
            break;
          case 'median':
            fillValue = await this.calculateMedian(dataset, rule.field);
            break;
          case 'mode':
            fillValue = await this.calculateMode(dataset, rule.field);
            break;
          case 'interpolation':
            fillValue = await this.interpolateValue(dataset, record, rule.field);
            break;
        }

        if (fillValue !== null && fillValue !== undefined) {
          record[rule.field] = fillValue;
          result.recordsModified++;
          result.changes.push({
            recordId: record.id,
            field: rule.field,
            action: 'filled',
            oldValue: value,
            newValue: fillValue,
            strategy: rule.strategy
          });
        }
      }
    }

    return result;
  }

  // 异常检测
  async detectAnomalies(dataset, schema) {
    const anomalies = {
      timestamp: Date.now(),
      datasetId: dataset.id,
      totalRecords: dataset.records.length,
      anomalousRecords: [],
      byField: {},
      methods: {}
    };

    for (const method of this.config.anomalyDetection.methods) {
      anomalies.methods[method] = await this.runAnomalyDetection(dataset, schema, method);
    }

    // 合并不同方法的结果
    anomalies.anomalousRecords = this.mergeAnomalyResults(anomalies.methods);

    return anomalies;
  }

  async runAnomalyDetection(dataset, schema, method) {
    switch (method) {
      case 'statistical':
        return await this.statisticalAnomalyDetection(dataset, schema);
      case 'isolation_forest':
        return await this.isolationForestDetection(dataset, schema);
      case 'clustering':
        return await this.clusteringAnomalyDetection(dataset, schema);
      default:
        throw new Error(`Unknown anomaly detection method: ${method}`);
    }
  }

  async statisticalAnomalyDetection(dataset, schema) {
    const anomalies = [];

    for (const field of schema.fields.filter(f => f.type === 'number')) {
      const values = dataset.records
        .map(record => record[field.name])
        .filter(value => value !== null && value !== undefined && !isNaN(value));

      if (values.length === 0) continue;

      const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
      const stdDev = Math.sqrt(variance);

      const threshold = this.config.anomalyDetection.sensitivity;
      const lowerBound = mean - (3 * stdDev);
      const upperBound = mean + (3 * stdDev);

      for (const record of dataset.records) {
        const value = record[field.name];
        if (value !== null && value !== undefined && !isNaN(value)) {
          if (value < lowerBound || value > upperBound) {
            anomalies.push({
              recordId: record.id,
              field: field.name,
              value: value,
              method: 'statistical',
              score: Math.abs(value - mean) / stdDev,
              bounds: { lower: lowerBound, upper: upperBound }
            });
          }
        }
      }
    }

    return anomalies;
  }

  calculateQualityScore(metrics) {
    const weights = {
      completeness: 0.25,
      accuracy: 0.30,
      consistency: 0.25,
      timeliness: 0.10,
      uniqueness: 0.05,
      validity: 0.05
    };

    let score = 0;
    let totalWeight = 0;

    for (const [metric, weight] of Object.entries(weights)) {
      if (metrics[metric] && metrics[metric].overall !== undefined) {
        score += metrics[metric].overall * weight;
        totalWeight += weight;
      }
    }

    return totalWeight > 0 ? score / totalWeight : 0;
  }

  generateQualityReport(assessment) {
    return {
      summary: {
        overallScore: assessment.score,
        grade: this.getQualityGrade(assessment.score),
        totalIssues: assessment.issues.length,
        criticalIssues: assessment.issues.filter(issue => issue.severity === 'critical').length
      },
      recommendations: this.generateRecommendations(assessment),
      trends: this.calculateQualityTrends(assessment.datasetId),
      actionItems: this.generateActionItems(assessment)
    };
  }

  getQualityGrade(score) {
    if (score >= 0.95) return 'A';
    if (score >= 0.90) return 'B';
    if (score >= 0.80) return 'C';
    if (score >= 0.70) return 'D';
    return 'F';
  }
}

// 使用示例
const qualityManager = new DataQualityManager({
  qualityThresholds: {
    completeness: 0.95,
    accuracy: 0.98,
    consistency: 0.99,
    timeliness: 0.90
  }
});

// 评估数据质量
const dataset = {
  id: 'user_conversations_2024',
  records: [
    { id: 1, userId: 'user1', message: 'Hello', timestamp: '2024-01-01T10:00:00Z', rating: 5 },
    { id: 2, userId: 'user2', message: '', timestamp: '2024-01-01T10:05:00Z', rating: null },
    // ... more records
  ]
};

const schema = {
  fields: [
    { name: 'userId', type: 'string', required: true },
    { name: 'message', type: 'string', required: true },
    { name: 'timestamp', type: 'datetime', required: true, temporal: true },
    { name: 'rating', type: 'number', range: { min: 1, max: 5 } }
  ],
  consistencyRules: [
    {
      name: 'rating_message_consistency',
      description: '有评分的记录必须有消息内容',
      condition: 'rating != null',
      requirement: 'message != null AND message != ""'
    }
  ]
};

async function runQualityAssessment() {
  const assessment = await qualityManager.assessDataQuality(dataset, schema);
  console.log('数据质量评估结果:', assessment);

  // 如果质量不达标，执行数据清洗
  if (assessment.score < 0.90) {
    const cleaningRules = [
      {
        name: 'fill_missing_ratings',
        type: 'fill_missing',
        field: 'rating',
        strategy: 'default',
        defaultValue: 3
      },
      {
        name: 'remove_empty_messages',
        type: 'remove_duplicates',
        keyFields: ['userId', 'message'],
        condition: 'message != ""'
      }
    ];

    const cleanedDataset = await qualityManager.cleanData(dataset, cleaningRules);
    console.log('数据清洗结果:', cleanedDataset.cleaningLog);
  }

  // 异常检测
  const anomalies = await qualityManager.detectAnomalies(dataset, schema);
  console.log('异常检测结果:', anomalies);
}
```

**技术要点**：
- 多维度数据质量评估
- 自动化数据清洗和修复
- 实时异常检测和告警
- 数据血缘追踪和影响分析

**实践项目**：
- 构建数据质量监控平台
- 实现自动化数据清洗流水线
- 开发异常检测告警系统

### 94. 业务指标监控
**掌握程度**：KPI仪表板，实时监控，告警系统

**技术解释与实现方案**：
业务指标监控是通过实时收集、分析和展示关键业务指标，帮助团队及时发现问题、优化决策的重要技术。在AI应用中，这包括用户活跃度、模型性能、业务转化率等多维度指标的监控。

**核心算法原理**：
- **指标计算**：实时和批量指标计算引擎
- **异常检测**：基于时间序列的异常识别
- **告警机制**：多级告警和智能降噪
- **趋势分析**：时间序列分析和预测

**技术实现方案**：
```javascript
// 业务指标监控系统
class BusinessMetricsMonitor {
  constructor(config = {}) {
    this.config = {
      updateInterval: 60000, // 1分钟
      retentionPeriod: 30 * 24 * 60 * 60 * 1000, // 30天
      alertThresholds: {
        error_rate: 0.05,
        response_time: 5000,
        user_satisfaction: 3.0
      },
      ...config
    };

    this.metrics = new Map();
    this.timeSeries = new Map();
    this.alerts = new Map();
    this.dashboards = new Map();
    this.subscribers = new Map();

    this.initializeMetrics();
    this.startMonitoring();
  }

  initializeMetrics() {
    // 定义核心业务指标
    this.defineMetric('user_active_count', {
      name: '活跃用户数',
      type: 'gauge',
      description: '当前活跃用户数量',
      unit: 'count',
      calculator: this.calculateActiveUsers.bind(this),
      alertRules: [
        { condition: 'value < 100', severity: 'warning', message: '活跃用户数过低' },
        { condition: 'value < 50', severity: 'critical', message: '活跃用户数严重不足' }
      ]
    });

    this.defineMetric('conversation_success_rate', {
      name: '对话成功率',
      type: 'percentage',
      description: '成功完成的对话占比',
      unit: '%',
      calculator: this.calculateConversationSuccessRate.bind(this),
      alertRules: [
        { condition: 'value < 0.8', severity: 'warning', message: '对话成功率下降' },
        { condition: 'value < 0.6', severity: 'critical', message: '对话成功率严重下降' }
      ]
    });

    this.defineMetric('ai_response_time', {
      name: 'AI响应时间',
      type: 'histogram',
      description: 'AI模型响应时间分布',
      unit: 'ms',
      calculator: this.calculateResponseTime.bind(this),
      alertRules: [
        { condition: 'p95 > 3000', severity: 'warning', message: 'AI响应时间过长' },
        { condition: 'p95 > 5000', severity: 'critical', message: 'AI响应时间严重超标' }
      ]
    });

    this.defineMetric('user_satisfaction_score', {
      name: '用户满意度',
      type: 'gauge',
      description: '用户满意度平均分',
      unit: 'score',
      calculator: this.calculateUserSatisfaction.bind(this),
      alertRules: [
        { condition: 'value < 3.5', severity: 'warning', message: '用户满意度下降' },
        { condition: 'value < 3.0', severity: 'critical', message: '用户满意度严重下降' }
      ]
    });

    this.defineMetric('error_rate', {
      name: '错误率',
      type: 'percentage',
      description: '系统错误率',
      unit: '%',
      calculator: this.calculateErrorRate.bind(this),
      alertRules: [
        { condition: 'value > 0.01', severity: 'warning', message: '系统错误率上升' },
        { condition: 'value > 0.05', severity: 'critical', message: '系统错误率过高' }
      ]
    });

    this.defineMetric('business_conversion_rate', {
      name: '业务转化率',
      type: 'percentage',
      description: '用户转化为付费客户的比例',
      unit: '%',
      calculator: this.calculateConversionRate.bind(this),
      alertRules: [
        { condition: 'value < 0.05', severity: 'warning', message: '转化率下降' },
        { condition: 'value < 0.02', severity: 'critical', message: '转化率严重下降' }
      ]
    });
  }

  defineMetric(id, definition) {
    this.metrics.set(id, {
      id,
      ...definition,
      lastValue: null,
      lastUpdated: null,
      history: []
    });

    this.timeSeries.set(id, []);
  }

  startMonitoring() {
    // 定期更新指标
    setInterval(async () => {
      await this.updateAllMetrics();
    }, this.config.updateInterval);

    // 清理历史数据
    setInterval(() => {
      this.cleanupHistoricalData();
    }, 24 * 60 * 60 * 1000); // 每天清理一次
  }

  async updateAllMetrics() {
    const updatePromises = Array.from(this.metrics.keys()).map(async (metricId) => {
      try {
        await this.updateMetric(metricId);
      } catch (error) {
        console.error(`Failed to update metric ${metricId}:`, error);
      }
    });

    await Promise.all(updatePromises);
  }

  async updateMetric(metricId) {
    const metric = this.metrics.get(metricId);
    if (!metric) return;

    const startTime = Date.now();

    try {
      // 计算指标值
      const value = await metric.calculator();
      const timestamp = Date.now();

      // 更新指标
      metric.lastValue = value;
      metric.lastUpdated = timestamp;
      metric.history.push({ value, timestamp });

      // 限制历史记录长度
      if (metric.history.length > 1000) {
        metric.history = metric.history.slice(-1000);
      }

      // 更新时间序列
      const timeSeries = this.timeSeries.get(metricId);
      timeSeries.push({ timestamp, value });

      // 限制时间序列长度
      const cutoffTime = timestamp - this.config.retentionPeriod;
      this.timeSeries.set(metricId, timeSeries.filter(point => point.timestamp > cutoffTime));

      // 检查告警规则
      await this.checkAlertRules(metricId, value, timestamp);

      // 通知订阅者
      this.notifySubscribers(metricId, value, timestamp);

      console.log(`Updated metric ${metricId}: ${value} (${Date.now() - startTime}ms)`);

    } catch (error) {
      console.error(`Error updating metric ${metricId}:`, error);

      // 记录错误指标
      this.recordMetricError(metricId, error);
    }
  }

  async calculateActiveUsers() {
    // 模拟计算活跃用户数
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    // 这里应该查询实际的用户活动数据
    const activeUsers = await this.queryDatabase(`
      SELECT COUNT(DISTINCT user_id) as count
      FROM user_activities
      WHERE timestamp > ?
    `, [oneHourAgo]);

    return activeUsers.count || 0;
  }

  async calculateConversationSuccessRate() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    const stats = await this.queryDatabase(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful
      FROM conversations
      WHERE created_at > ?
    `, [oneHourAgo]);

    return stats.total > 0 ? stats.successful / stats.total : 1;
  }

  async calculateResponseTime() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    const responseTimes = await this.queryDatabase(`
      SELECT response_time
      FROM ai_requests
      WHERE timestamp > ?
      ORDER BY response_time
    `, [oneHourAgo]);

    if (responseTimes.length === 0) return { avg: 0, p50: 0, p95: 0, p99: 0 };

    const times = responseTimes.map(r => r.response_time);
    const sorted = times.sort((a, b) => a - b);

    return {
      avg: times.reduce((sum, time) => sum + time, 0) / times.length,
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }

  async calculateUserSatisfaction() {
    const now = Date.now();
    const oneDayAgo = now - 24 * 60 * 60 * 1000;

    const satisfaction = await this.queryDatabase(`
      SELECT AVG(rating) as avg_rating
      FROM user_feedback
      WHERE created_at > ? AND rating IS NOT NULL
    `, [oneDayAgo]);

    return satisfaction.avg_rating || 0;
  }

  async calculateErrorRate() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    const stats = await this.queryDatabase(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as errors
      FROM api_requests
      WHERE timestamp > ?
    `, [oneHourAgo]);

    return stats.total > 0 ? stats.errors / stats.total : 0;
  }

  async calculateConversionRate() {
    const now = Date.now();
    const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;

    const stats = await this.queryDatabase(`
      SELECT
        COUNT(DISTINCT u.id) as total_users,
        COUNT(DISTINCT p.user_id) as converted_users
      FROM users u
      LEFT JOIN purchases p ON u.id = p.user_id AND p.created_at > ?
      WHERE u.created_at > ?
    `, [oneWeekAgo, oneWeekAgo]);

    return stats.total_users > 0 ? stats.converted_users / stats.total_users : 0;
  }

  async checkAlertRules(metricId, value, timestamp) {
    const metric = this.metrics.get(metricId);
    if (!metric || !metric.alertRules) return;

    for (const rule of metric.alertRules) {
      const shouldAlert = this.evaluateAlertCondition(rule.condition, value, metric);

      if (shouldAlert) {
        await this.triggerAlert(metricId, rule, value, timestamp);
      } else {
        // 检查是否需要恢复告警
        await this.checkAlertRecovery(metricId, rule, value, timestamp);
      }
    }
  }

  evaluateAlertCondition(condition, value, metric) {
    // 简单的条件评估器
    try {
      // 替换条件中的变量
      let evaluableCondition = condition
        .replace(/value/g, value)
        .replace(/p95/g, value.p95 || 0)
        .replace(/p99/g, value.p99 || 0)
        .replace(/avg/g, value.avg || value);

      // 安全的条件评估
      return Function(`"use strict"; return (${evaluableCondition})`)();
    } catch (error) {
      console.error(`Error evaluating alert condition: ${condition}`, error);
      return false;
    }
  }

  async triggerAlert(metricId, rule, value, timestamp) {
    const alertId = `${metricId}_${rule.severity}`;
    const existingAlert = this.alerts.get(alertId);

    // 避免重复告警
    if (existingAlert && timestamp - existingAlert.lastTriggered < 5 * 60 * 1000) {
      return;
    }

    const alert = {
      id: alertId,
      metricId,
      severity: rule.severity,
      message: rule.message,
      value,
      condition: rule.condition,
      timestamp,
      lastTriggered: timestamp,
      count: existingAlert ? existingAlert.count + 1 : 1,
      status: 'active'
    };

    this.alerts.set(alertId, alert);

    // 发送告警通知
    await this.sendAlertNotification(alert);

    console.log(`Alert triggered: ${alert.message} (${metricId}: ${value})`);
  }

  async sendAlertNotification(alert) {
    const notification = {
      title: `${alert.severity.toUpperCase()}: ${alert.message}`,
      body: `指标: ${alert.metricId}\n当前值: ${JSON.stringify(alert.value)}\n触发条件: ${alert.condition}`,
      timestamp: alert.timestamp,
      severity: alert.severity
    };

    // 发送到不同的通知渠道
    const channels = this.getNotificationChannels(alert.severity);

    for (const channel of channels) {
      try {
        await this.sendToChannel(channel, notification);
      } catch (error) {
        console.error(`Failed to send alert to ${channel}:`, error);
      }
    }
  }

  getNotificationChannels(severity) {
    const channels = ['console'];

    if (severity === 'warning') {
      channels.push('slack', 'email');
    } else if (severity === 'critical') {
      channels.push('slack', 'email', 'sms', 'pagerduty');
    }

    return channels;
  }

  async sendToChannel(channel, notification) {
    switch (channel) {
      case 'console':
        console.log(`[${notification.severity}] ${notification.title}: ${notification.body}`);
        break;
      case 'slack':
        await this.sendSlackNotification(notification);
        break;
      case 'email':
        await this.sendEmailNotification(notification);
        break;
      case 'sms':
        await this.sendSmsNotification(notification);
        break;
      case 'pagerduty':
        await this.sendPagerDutyNotification(notification);
        break;
    }
  }

  // 创建仪表板
  createDashboard(name, config) {
    const dashboard = {
      name,
      id: `dashboard_${Date.now()}`,
      widgets: [],
      layout: config.layout || 'grid',
      refreshInterval: config.refreshInterval || 60000,
      ...config
    };

    this.dashboards.set(dashboard.id, dashboard);
    return dashboard;
  }

  addWidget(dashboardId, widget) {
    const dashboard = this.dashboards.get(dashboardId);
    if (!dashboard) return;

    const widgetWithId = {
      ...widget,
      id: `widget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    dashboard.widgets.push(widgetWithId);
    return widgetWithId;
  }

  // 获取仪表板数据
  async getDashboardData(dashboardId) {
    const dashboard = this.dashboards.get(dashboardId);
    if (!dashboard) return null;

    const data = {
      dashboard: { ...dashboard },
      widgets: []
    };

    for (const widget of dashboard.widgets) {
      const widgetData = await this.getWidgetData(widget);
      data.widgets.push(widgetData);
    }

    return data;
  }

  async getWidgetData(widget) {
    const { type, metricId, timeRange = '1h' } = widget;
    const metric = this.metrics.get(metricId);

    if (!metric) {
      return { ...widget, error: 'Metric not found' };
    }

    const timeSeries = this.getTimeSeriesData(metricId, timeRange);

    return {
      ...widget,
      currentValue: metric.lastValue,
      lastUpdated: metric.lastUpdated,
      timeSeries: timeSeries,
      status: this.getMetricStatus(metricId)
    };
  }

  getTimeSeriesData(metricId, timeRange) {
    const timeSeries = this.timeSeries.get(metricId) || [];
    const now = Date.now();

    let cutoffTime;
    switch (timeRange) {
      case '1h': cutoffTime = now - 60 * 60 * 1000; break;
      case '24h': cutoffTime = now - 24 * 60 * 60 * 1000; break;
      case '7d': cutoffTime = now - 7 * 24 * 60 * 60 * 1000; break;
      case '30d': cutoffTime = now - 30 * 24 * 60 * 60 * 1000; break;
      default: cutoffTime = now - 60 * 60 * 1000;
    }

    return timeSeries.filter(point => point.timestamp > cutoffTime);
  }

  getMetricStatus(metricId) {
    const activeAlerts = Array.from(this.alerts.values())
      .filter(alert => alert.metricId === metricId && alert.status === 'active');

    if (activeAlerts.some(alert => alert.severity === 'critical')) {
      return 'critical';
    } else if (activeAlerts.some(alert => alert.severity === 'warning')) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }

  // 订阅指标更新
  subscribe(metricId, callback) {
    if (!this.subscribers.has(metricId)) {
      this.subscribers.set(metricId, new Set());
    }

    this.subscribers.get(metricId).add(callback);

    return () => {
      this.subscribers.get(metricId).delete(callback);
    };
  }

  notifySubscribers(metricId, value, timestamp) {
    const callbacks = this.subscribers.get(metricId);
    if (!callbacks) return;

    callbacks.forEach(callback => {
      try {
        callback({ metricId, value, timestamp });
      } catch (error) {
        console.error('Error in metric subscriber callback:', error);
      }
    });
  }

  // 模拟数据库查询
  async queryDatabase(query, params) {
    // 这里应该连接到实际的数据库
    // 返回模拟数据
    await new Promise(resolve => setTimeout(resolve, 100));

    if (query.includes('user_activities')) {
      return { count: Math.floor(Math.random() * 200) + 50 };
    } else if (query.includes('conversations')) {
      return { total: 100, successful: 85 };
    } else if (query.includes('ai_requests')) {
      return Array.from({ length: 100 }, () => ({
        response_time: Math.floor(Math.random() * 3000) + 500
      }));
    } else if (query.includes('user_feedback')) {
      return { avg_rating: 3.5 + Math.random() * 1.5 };
    } else if (query.includes('api_requests')) {
      return { total: 1000, errors: Math.floor(Math.random() * 20) };
    } else if (query.includes('purchases')) {
      return { total_users: 1000, converted_users: Math.floor(Math.random() * 50) + 20 };
    }

    return {};
  }
}

// 使用示例
const monitor = new BusinessMetricsMonitor({
  updateInterval: 30000, // 30秒更新一次
  alertThresholds: {
    error_rate: 0.02,
    response_time: 3000,
    user_satisfaction: 3.5
  }
});

// 创建主要业务仪表板
const mainDashboard = monitor.createDashboard('主要业务指标', {
  layout: 'grid',
  refreshInterval: 60000
});

// 添加仪表板组件
monitor.addWidget(mainDashboard.id, {
  type: 'metric_card',
  metricId: 'user_active_count',
  title: '活跃用户',
  size: 'small'
});

monitor.addWidget(mainDashboard.id, {
  type: 'line_chart',
  metricId: 'conversation_success_rate',
  title: '对话成功率趋势',
  timeRange: '24h',
  size: 'medium'
});

monitor.addWidget(mainDashboard.id, {
  type: 'histogram',
  metricId: 'ai_response_time',
  title: 'AI响应时间分布',
  timeRange: '1h',
  size: 'large'
});

// 订阅关键指标更新
monitor.subscribe('user_active_count', ({ metricId, value, timestamp }) => {
  console.log(`活跃用户数更新: ${value} (${new Date(timestamp).toLocaleString()})`);
});

// 获取仪表板数据
async function displayDashboard() {
  const dashboardData = await monitor.getDashboardData(mainDashboard.id);
  console.log('仪表板数据:', JSON.stringify(dashboardData, null, 2));
}

// 每分钟显示一次仪表板
setInterval(displayDashboard, 60000);
```

**技术要点**：
- 实时指标计算和更新
- 多级告警和智能降噪
- 可视化仪表板和图表
- 时间序列分析和趋势预测

**实践项目**：
- 构建业务监控仪表板
- 实现智能告警系统
- 开发指标分析平台

### 95. 客户关系管理
**掌握程度**：CRM系统集成，客户画像，生命周期管理

**技术解释与实现方案**：
客户关系管理（CRM）是通过技术手段管理和分析客户交互数据，提升客户满意度和业务价值的重要系统。在AI应用中，这包括智能客户画像、个性化服务推荐、客户生命周期自动化管理等功能。

**核心算法原理**：
- **客户画像**：基于多维数据的用户特征建模
- **生命周期管理**：客户价值评估和阶段识别
- **个性化推荐**：基于行为和偏好的智能推荐
- **预测分析**：客户流失预测和价值预测

**技术实现方案**：
```javascript
// 客户关系管理系统
class CustomerRelationshipManager {
  constructor(config = {}) {
    this.config = {
      segmentationRules: {
        highValue: { minRevenue: 10000, minEngagement: 0.8 },
        mediumValue: { minRevenue: 1000, minEngagement: 0.5 },
        lowValue: { minRevenue: 0, minEngagement: 0.2 }
      },
      lifecycleStages: ['prospect', 'lead', 'customer', 'advocate', 'churned'],
      retentionThreshold: 0.7,
      ...config
    };

    this.customers = new Map();
    this.interactions = new Map();
    this.segments = new Map();
    this.campaigns = new Map();
    this.predictiveModels = new Map();

    this.initializeModels();
  }

  initializeModels() {
    // 初始化预测模型
    this.predictiveModels.set('churn_prediction', new ChurnPredictionModel());
    this.predictiveModels.set('ltv_prediction', new LifetimeValueModel());
    this.predictiveModels.set('next_best_action', new NextBestActionModel());
  }

  // 创建或更新客户档案
  async createOrUpdateCustomer(customerId, data) {
    const existingCustomer = this.customers.get(customerId);

    const customer = {
      id: customerId,
      ...existingCustomer,
      ...data,
      lastUpdated: Date.now(),
      profile: await this.buildCustomerProfile(customerId, data),
      segment: await this.determineCustomerSegment(customerId, data),
      lifecycleStage: await this.determineLifecycleStage(customerId, data),
      predictions: await this.generatePredictions(customerId, data)
    };

    this.customers.set(customerId, customer);

    // 触发客户更新事件
    await this.onCustomerUpdated(customer);

    return customer;
  }

  async buildCustomerProfile(customerId, data) {
    const interactions = await this.getCustomerInteractions(customerId);
    const transactionHistory = await this.getTransactionHistory(customerId);
    const behaviorData = await this.getBehaviorData(customerId);

    const profile = {
      demographics: {
        age: data.age,
        gender: data.gender,
        location: data.location,
        occupation: data.occupation
      },

      behavioral: {
        totalInteractions: interactions.length,
        avgSessionDuration: this.calculateAvgSessionDuration(interactions),
        preferredChannels: this.identifyPreferredChannels(interactions),
        activityPattern: this.analyzeActivityPattern(interactions),
        engagementScore: this.calculateEngagementScore(interactions, behaviorData)
      },

      transactional: {
        totalRevenue: transactionHistory.reduce((sum, t) => sum + t.amount, 0),
        avgOrderValue: this.calculateAvgOrderValue(transactionHistory),
        purchaseFrequency: this.calculatePurchaseFrequency(transactionHistory),
        lastPurchaseDate: this.getLastPurchaseDate(transactionHistory),
        preferredProducts: this.identifyPreferredProducts(transactionHistory)
      },

      preferences: {
        communicationPreferences: data.communicationPreferences || {},
        productInterests: this.inferProductInterests(interactions, transactionHistory),
        contentPreferences: this.analyzeContentPreferences(behaviorData),
        servicePreferences: this.analyzeServicePreferences(interactions)
      },

      satisfaction: {
        overallScore: await this.calculateSatisfactionScore(customerId),
        npsScore: data.npsScore,
        feedbackSentiment: await this.analyzeFeedbackSentiment(customerId),
        supportTicketHistory: await this.getSupportHistory(customerId)
      }
    };

    return profile;
  }

  async determineCustomerSegment(customerId, data) {
    const profile = await this.buildCustomerProfile(customerId, data);
    const { behavioral, transactional } = profile;

    // 基于RFM模型的客户分段
    const rfmScore = this.calculateRFMScore(transactional);

    // 基于行为的分段
    const behaviorScore = behavioral.engagementScore;

    // 综合评分
    const totalRevenue = transactional.totalRevenue;
    const engagementLevel = behaviorScore;

    if (totalRevenue >= this.config.segmentationRules.highValue.minRevenue &&
        engagementLevel >= this.config.segmentationRules.highValue.minEngagement) {
      return {
        primary: 'high_value',
        secondary: this.getSecondarySegment(profile),
        rfmScore,
        confidence: 0.9
      };
    } else if (totalRevenue >= this.config.segmentationRules.mediumValue.minRevenue &&
               engagementLevel >= this.config.segmentationRules.mediumValue.minEngagement) {
      return {
        primary: 'medium_value',
        secondary: this.getSecondarySegment(profile),
        rfmScore,
        confidence: 0.8
      };
    } else {
      return {
        primary: 'low_value',
        secondary: this.getSecondarySegment(profile),
        rfmScore,
        confidence: 0.7
      };
    }
  }

  calculateRFMScore(transactional) {
    const now = Date.now();
    const daysSinceLastPurchase = transactional.lastPurchaseDate ?
      (now - transactional.lastPurchaseDate) / (24 * 60 * 60 * 1000) : 365;

    // Recency (1-5, 5 is best)
    let recency = 5;
    if (daysSinceLastPurchase > 365) recency = 1;
    else if (daysSinceLastPurchase > 180) recency = 2;
    else if (daysSinceLastPurchase > 90) recency = 3;
    else if (daysSinceLastPurchase > 30) recency = 4;

    // Frequency (1-5, 5 is best)
    const frequency = Math.min(5, Math.floor(transactional.purchaseFrequency * 5) + 1);

    // Monetary (1-5, 5 is best)
    let monetary = 1;
    if (transactional.totalRevenue > 10000) monetary = 5;
    else if (transactional.totalRevenue > 5000) monetary = 4;
    else if (transactional.totalRevenue > 1000) monetary = 3;
    else if (transactional.totalRevenue > 100) monetary = 2;

    return {
      recency,
      frequency,
      monetary,
      total: recency + frequency + monetary,
      segment: this.getRFMSegment(recency, frequency, monetary)
    };
  }

  getRFMSegment(r, f, m) {
    if (r >= 4 && f >= 4 && m >= 4) return 'champions';
    if (r >= 3 && f >= 3 && m >= 3) return 'loyal_customers';
    if (r >= 4 && f <= 2) return 'new_customers';
    if (r >= 3 && f >= 3 && m <= 2) return 'potential_loyalists';
    if (r >= 3 && f <= 2 && m <= 2) return 'promising';
    if (r <= 2 && f >= 3 && m >= 3) return 'customers_needing_attention';
    if (r <= 2 && f >= 2 && m >= 2) return 'about_to_sleep';
    if (r <= 2 && f >= 3 && m <= 2) return 'at_risk';
    if (r <= 1 && f >= 2) return 'cannot_lose_them';
    return 'hibernating';
  }

  async determineLifecycleStage(customerId, data) {
    const interactions = await this.getCustomerInteractions(customerId);
    const transactions = await this.getTransactionHistory(customerId);
    const profile = await this.buildCustomerProfile(customerId, data);

    // 基于行为和交易历史确定生命周期阶段
    if (transactions.length === 0) {
      if (interactions.length === 0) {
        return { stage: 'prospect', confidence: 0.9 };
      } else if (interactions.length < 5) {
        return { stage: 'lead', confidence: 0.8 };
      }
    }

    if (transactions.length > 0) {
      const daysSinceLastPurchase = (Date.now() - this.getLastPurchaseDate(transactions)) / (24 * 60 * 60 * 1000);
      const avgSatisfaction = profile.satisfaction.overallScore;

      if (daysSinceLastPurchase > 365) {
        return { stage: 'churned', confidence: 0.9 };
      } else if (avgSatisfaction >= 4.5 && profile.behavioral.engagementScore >= 0.8) {
        return { stage: 'advocate', confidence: 0.85 };
      } else {
        return { stage: 'customer', confidence: 0.8 };
      }
    }

    return { stage: 'prospect', confidence: 0.6 };
  }

  async generatePredictions(customerId, data) {
    const predictions = {};

    // 流失预测
    const churnModel = this.predictiveModels.get('churn_prediction');
    predictions.churnRisk = await churnModel.predict(customerId, data);

    // 生命周期价值预测
    const ltvModel = this.predictiveModels.get('ltv_prediction');
    predictions.lifetimeValue = await ltvModel.predict(customerId, data);

    // 下一步最佳行动
    const nbaModel = this.predictiveModels.get('next_best_action');
    predictions.nextBestAction = await nbaModel.predict(customerId, data);

    return predictions;
  }

  // 记录客户交互
  async recordInteraction(customerId, interaction) {
    const interactionRecord = {
      id: `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      customerId,
      timestamp: Date.now(),
      ...interaction
    };

    if (!this.interactions.has(customerId)) {
      this.interactions.set(customerId, []);
    }

    this.interactions.get(customerId).push(interactionRecord);

    // 更新客户档案
    await this.updateCustomerFromInteraction(customerId, interactionRecord);

    return interactionRecord;
  }

  async updateCustomerFromInteraction(customerId, interaction) {
    const customer = this.customers.get(customerId);
    if (!customer) return;

    // 更新最后交互时间
    customer.lastInteraction = interaction.timestamp;

    // 更新交互统计
    customer.totalInteractions = (customer.totalInteractions || 0) + 1;

    // 重新计算客户画像
    customer.profile = await this.buildCustomerProfile(customerId, customer);
    customer.segment = await this.determineCustomerSegment(customerId, customer);
    customer.lifecycleStage = await this.determineLifecycleStage(customerId, customer);

    this.customers.set(customerId, customer);
  }

  // 个性化推荐
  async getPersonalizedRecommendations(customerId, context = {}) {
    const customer = this.customers.get(customerId);
    if (!customer) return [];

    const recommendations = [];

    // 基于客户画像的推荐
    const profileRecommendations = await this.getProfileBasedRecommendations(customer);
    recommendations.push(...profileRecommendations);

    // 基于行为的推荐
    const behaviorRecommendations = await this.getBehaviorBasedRecommendations(customer);
    recommendations.push(...behaviorRecommendations);

    // 基于协同过滤的推荐
    const collaborativeRecommendations = await this.getCollaborativeRecommendations(customer);
    recommendations.push(...collaborativeRecommendations);

    // 基于上下文的推荐
    const contextualRecommendations = await this.getContextualRecommendations(customer, context);
    recommendations.push(...contextualRecommendations);

    // 排序和去重
    return this.rankAndDeduplicateRecommendations(recommendations, customer);
  }

  async getProfileBasedRecommendations(customer) {
    const recommendations = [];
    const { profile } = customer;

    // 基于偏好的产品推荐
    if (profile.preferences.productInterests) {
      for (const interest of profile.preferences.productInterests) {
        recommendations.push({
          type: 'product',
          item: interest,
          reason: 'based_on_interests',
          confidence: 0.7,
          source: 'profile'
        });
      }
    }

    // 基于生命周期阶段的推荐
    const stage = customer.lifecycleStage.stage;
    const stageRecommendations = await this.getStageBasedRecommendations(stage);
    recommendations.push(...stageRecommendations);

    return recommendations;
  }

  async getBehaviorBasedRecommendations(customer) {
    const recommendations = [];
    const interactions = await this.getCustomerInteractions(customer.id);

    // 分析最近的行为模式
    const recentInteractions = interactions.slice(-10);
    const behaviorPattern = this.analyzeBehaviorPattern(recentInteractions);

    // 基于行为模式推荐
    if (behaviorPattern.frequentActions.includes('view_product')) {
      recommendations.push({
        type: 'action',
        item: 'product_demo',
        reason: 'frequent_product_views',
        confidence: 0.8,
        source: 'behavior'
      });
    }

    return recommendations;
  }

  // 客户生命周期自动化
  async automateLifecycleManagement(customerId) {
    const customer = this.customers.get(customerId);
    if (!customer) return;

    const stage = customer.lifecycleStage.stage;
    const actions = [];

    switch (stage) {
      case 'prospect':
        actions.push(await this.createWelcomeSequence(customer));
        break;
      case 'lead':
        actions.push(await this.createNurturingSequence(customer));
        break;
      case 'customer':
        actions.push(await this.createRetentionSequence(customer));
        break;
      case 'advocate':
        actions.push(await this.createAdvocacySequence(customer));
        break;
      case 'churned':
        actions.push(await this.createWinBackSequence(customer));
        break;
    }

    // 执行自动化动作
    for (const action of actions) {
      await this.executeAutomatedAction(customer, action);
    }

    return actions;
  }

  async createWelcomeSequence(customer) {
    return {
      type: 'email_sequence',
      name: 'welcome_series',
      emails: [
        {
          delay: 0,
          template: 'welcome_email',
          subject: '欢迎加入我们！',
          personalization: {
            name: customer.profile.demographics.name,
            interests: customer.profile.preferences.productInterests
          }
        },
        {
          delay: 24 * 60 * 60 * 1000, // 1天后
          template: 'getting_started',
          subject: '开始您的AI之旅',
          personalization: {
            recommendedFeatures: await this.getRecommendedFeatures(customer)
          }
        },
        {
          delay: 7 * 24 * 60 * 60 * 1000, // 7天后
          template: 'value_proposition',
          subject: '发现AI的强大功能',
          personalization: {
            useCases: await this.getRelevantUseCases(customer)
          }
        }
      ]
    };
  }

  async executeAutomatedAction(customer, action) {
    switch (action.type) {
      case 'email_sequence':
        await this.scheduleEmailSequence(customer, action);
        break;
      case 'push_notification':
        await this.schedulePushNotification(customer, action);
        break;
      case 'in_app_message':
        await this.scheduleInAppMessage(customer, action);
        break;
      case 'sales_task':
        await this.createSalesTask(customer, action);
        break;
    }
  }

  // 客户价值分析
  async analyzeCustomerValue(customerId) {
    const customer = this.customers.get(customerId);
    if (!customer) return null;

    const analysis = {
      currentValue: await this.calculateCurrentValue(customer),
      predictedValue: customer.predictions.lifetimeValue,
      valueSegment: customer.segment.primary,
      valueDrivers: await this.identifyValueDrivers(customer),
      riskFactors: await this.identifyRiskFactors(customer),
      opportunities: await this.identifyOpportunities(customer)
    };

    return analysis;
  }

  async calculateCurrentValue(customer) {
    const transactions = await this.getTransactionHistory(customer.id);
    const totalRevenue = transactions.reduce((sum, t) => sum + t.amount, 0);
    const acquisitionCost = customer.acquisitionCost || 0;
    const serviceCost = await this.calculateServiceCost(customer);

    return {
      totalRevenue,
      netValue: totalRevenue - acquisitionCost - serviceCost,
      avgOrderValue: totalRevenue / Math.max(transactions.length, 1),
      profitMargin: this.calculateProfitMargin(totalRevenue, acquisitionCost, serviceCost)
    };
  }

  // 获取客户洞察
  async getCustomerInsights(customerId) {
    const customer = this.customers.get(customerId);
    if (!customer) return null;

    const insights = {
      behaviorInsights: await this.analyzeBehaviorInsights(customer),
      preferenceInsights: await this.analyzePreferenceInsights(customer),
      satisfactionInsights: await this.analyzeSatisfactionInsights(customer),
      engagementInsights: await this.analyzeEngagementInsights(customer),
      predictiveInsights: await this.analyzePredictiveInsights(customer)
    };

    return insights;
  }

  async analyzeBehaviorInsights(customer) {
    const interactions = await this.getCustomerInteractions(customer.id);

    return {
      mostActiveHours: this.findMostActiveHours(interactions),
      preferredChannels: this.identifyPreferredChannels(interactions),
      sessionPatterns: this.analyzeSessionPatterns(interactions),
      contentEngagement: this.analyzeContentEngagement(interactions),
      featureUsage: this.analyzeFeatureUsage(interactions)
    };
  }
}

// 流失预测模型
class ChurnPredictionModel {
  async predict(customerId, customerData) {
    // 简化的流失预测逻辑
    const features = this.extractFeatures(customerData);
    const churnScore = this.calculateChurnScore(features);

    return {
      score: churnScore,
      risk: churnScore > 0.7 ? 'high' : churnScore > 0.4 ? 'medium' : 'low',
      factors: this.identifyChurnFactors(features),
      recommendations: this.getChurnPreventionRecommendations(churnScore, features)
    };
  }

  extractFeatures(customerData) {
    const profile = customerData.profile || {};
    const now = Date.now();

    return {
      daysSinceLastInteraction: profile.behavioral?.lastInteractionDate ?
        (now - profile.behavioral.lastInteractionDate) / (24 * 60 * 60 * 1000) : 365,
      engagementScore: profile.behavioral?.engagementScore || 0,
      satisfactionScore: profile.satisfaction?.overallScore || 3,
      totalRevenue: profile.transactional?.totalRevenue || 0,
      supportTickets: profile.satisfaction?.supportTicketHistory?.length || 0,
      featureUsage: profile.behavioral?.featureUsage || 0
    };
  }

  calculateChurnScore(features) {
    let score = 0;

    // 基于最后交互时间
    if (features.daysSinceLastInteraction > 90) score += 0.3;
    else if (features.daysSinceLastInteraction > 30) score += 0.1;

    // 基于参与度
    if (features.engagementScore < 0.3) score += 0.2;
    else if (features.engagementScore < 0.5) score += 0.1;

    // 基于满意度
    if (features.satisfactionScore < 3) score += 0.2;
    else if (features.satisfactionScore < 3.5) score += 0.1;

    // 基于支持票据
    if (features.supportTickets > 5) score += 0.15;
    else if (features.supportTickets > 2) score += 0.05;

    // 基于功能使用
    if (features.featureUsage < 0.2) score += 0.15;

    return Math.min(score, 1);
  }

  identifyChurnFactors(features) {
    const factors = [];

    if (features.daysSinceLastInteraction > 30) {
      factors.push('低活跃度');
    }
    if (features.engagementScore < 0.5) {
      factors.push('参与度下降');
    }
    if (features.satisfactionScore < 3.5) {
      factors.push('满意度不高');
    }
    if (features.supportTickets > 2) {
      factors.push('支持问题较多');
    }

    return factors;
  }

  getChurnPreventionRecommendations(churnScore, features) {
    const recommendations = [];

    if (churnScore > 0.7) {
      recommendations.push('立即安排客户成功经理联系');
      recommendations.push('提供个性化优惠或折扣');
      recommendations.push('邀请参加产品培训或演示');
    } else if (churnScore > 0.4) {
      recommendations.push('发送个性化内容和使用技巧');
      recommendations.push('邀请参加用户社区活动');
      recommendations.push('收集反馈并改进产品体验');
    }

    return recommendations;
  }
}

// 生命周期价值预测模型
class LifetimeValueModel {
  async predict(customerId, customerData) {
    const features = this.extractLTVFeatures(customerData);
    const ltvPrediction = this.calculateLTV(features);

    return {
      predictedLTV: ltvPrediction.value,
      confidence: ltvPrediction.confidence,
      timeHorizon: '12_months',
      factors: ltvPrediction.factors,
      recommendations: this.getLTVOptimizationRecommendations(ltvPrediction)
    };
  }

  extractLTVFeatures(customerData) {
    const profile = customerData.profile || {};

    return {
      avgOrderValue: profile.transactional?.avgOrderValue || 0,
      purchaseFrequency: profile.transactional?.purchaseFrequency || 0,
      customerAge: profile.demographics?.customerAge || 0,
      engagementScore: profile.behavioral?.engagementScore || 0,
      satisfactionScore: profile.satisfaction?.overallScore || 3,
      totalRevenue: profile.transactional?.totalRevenue || 0
    };
  }

  calculateLTV(features) {
    // 简化的LTV计算
    const monthlyValue = features.avgOrderValue * features.purchaseFrequency;
    const retentionRate = this.calculateRetentionRate(features);
    const predictedLifespan = retentionRate > 0 ? 1 / (1 - retentionRate) : 12;

    const ltv = monthlyValue * predictedLifespan;

    return {
      value: ltv,
      confidence: this.calculateConfidence(features),
      factors: {
        monthlyValue,
        retentionRate,
        predictedLifespan
      }
    };
  }

  calculateRetentionRate(features) {
    let retentionRate = 0.8; // 基础保留率

    // 基于满意度调整
    if (features.satisfactionScore > 4) retentionRate += 0.1;
    else if (features.satisfactionScore < 3) retentionRate -= 0.2;

    // 基于参与度调整
    if (features.engagementScore > 0.7) retentionRate += 0.05;
    else if (features.engagementScore < 0.3) retentionRate -= 0.15;

    return Math.max(0.1, Math.min(0.95, retentionRate));
  }

  calculateConfidence(features) {
    let confidence = 0.7;

    // 基于数据完整性
    const dataCompleteness = Object.values(features).filter(v => v > 0).length / Object.keys(features).length;
    confidence *= dataCompleteness;

    // 基于客户历史
    if (features.customerAge > 6) confidence += 0.1;
    if (features.totalRevenue > 1000) confidence += 0.1;

    return Math.min(0.95, confidence);
  }
}

// 下一步最佳行动模型
class NextBestActionModel {
  async predict(customerId, customerData) {
    const context = this.analyzeCustomerContext(customerData);
    const actions = await this.generateActionCandidates(context);
    const rankedActions = this.rankActions(actions, context);

    return {
      recommendedAction: rankedActions[0],
      alternativeActions: rankedActions.slice(1, 4),
      reasoning: this.explainRecommendation(rankedActions[0], context)
    };
  }

  analyzeCustomerContext(customerData) {
    return {
      segment: customerData.segment?.primary,
      lifecycleStage: customerData.lifecycleStage?.stage,
      churnRisk: customerData.predictions?.churnRisk?.risk,
      engagementLevel: customerData.profile?.behavioral?.engagementScore,
      satisfactionLevel: customerData.profile?.satisfaction?.overallScore,
      recentBehavior: customerData.recentInteractions || []
    };
  }

  async generateActionCandidates(context) {
    const candidates = [];

    // 基于生命周期阶段的行动
    candidates.push(...this.getLifecycleActions(context.lifecycleStage));

    // 基于流失风险的行动
    if (context.churnRisk === 'high') {
      candidates.push(...this.getChurnPreventionActions());
    }

    // 基于参与度的行动
    if (context.engagementLevel < 0.5) {
      candidates.push(...this.getEngagementActions());
    }

    // 基于满意度的行动
    if (context.satisfactionLevel < 3.5) {
      candidates.push(...this.getSatisfactionActions());
    }

    return candidates;
  }

  getLifecycleActions(stage) {
    const actions = {
      prospect: [
        { type: 'email', action: 'send_welcome_series', priority: 0.8 },
        { type: 'content', action: 'share_getting_started_guide', priority: 0.7 }
      ],
      lead: [
        { type: 'sales', action: 'schedule_demo', priority: 0.9 },
        { type: 'email', action: 'send_case_studies', priority: 0.6 }
      ],
      customer: [
        { type: 'support', action: 'proactive_check_in', priority: 0.7 },
        { type: 'upsell', action: 'recommend_premium_features', priority: 0.6 }
      ],
      advocate: [
        { type: 'referral', action: 'invite_referral_program', priority: 0.8 },
        { type: 'content', action: 'request_testimonial', priority: 0.7 }
      ]
    };

    return actions[stage] || [];
  }

  rankActions(actions, context) {
    return actions
      .map(action => ({
        ...action,
        score: this.calculateActionScore(action, context)
      }))
      .sort((a, b) => b.score - a.score);
  }

  calculateActionScore(action, context) {
    let score = action.priority || 0.5;

    // 基于上下文调整分数
    if (context.churnRisk === 'high' && action.type === 'retention') {
      score += 0.3;
    }

    if (context.engagementLevel < 0.3 && action.type === 'engagement') {
      score += 0.2;
    }

    if (context.satisfactionLevel < 3 && action.type === 'support') {
      score += 0.25;
    }

    return Math.min(1, score);
  }
}

// 使用示例
const crm = new CustomerRelationshipManager();

// 创建客户档案
async function createCustomerProfile() {
  const customer = await crm.createOrUpdateCustomer('customer_123', {
    name: '张三',
    email: '<EMAIL>',
    age: 35,
    gender: 'male',
    location: '北京',
    occupation: '产品经理',
    acquisitionCost: 500,
    communicationPreferences: {
      email: true,
      sms: false,
      push: true
    }
  });

  console.log('客户档案创建完成:', customer);

  // 记录客户交互
  await crm.recordInteraction('customer_123', {
    type: 'page_view',
    page: '/product-demo',
    duration: 300,
    source: 'email_campaign'
  });

  // 获取个性化推荐
  const recommendations = await crm.getPersonalizedRecommendations('customer_123');
  console.log('个性化推荐:', recommendations);

  // 执行生命周期自动化
  const automatedActions = await crm.automateLifecycleManagement('customer_123');
  console.log('自动化行动:', automatedActions);

  // 分析客户价值
  const valueAnalysis = await crm.analyzeCustomerValue('customer_123');
  console.log('客户价值分析:', valueAnalysis);

  // 获取客户洞察
  const insights = await crm.getCustomerInsights('customer_123');
  console.log('客户洞察:', insights);
}
```

**技术要点**：
- 多维度客户画像构建
- 智能客户分段和生命周期管理
- 预测性分析和个性化推荐
- 自动化营销和客户服务

**实践项目**：
- 构建智能CRM系统
- 实现客户画像分析平台
- 开发个性化推荐引擎

### 96. 供应链管理
**掌握程度**：供应商管理，库存优化，需求预测

**技术解释与实现方案**：
供应链管理是通过技术手段优化从供应商到最终客户的整个价值链流程。在AI应用中，这包括智能需求预测、自动化采购决策、供应商风险评估等功能，确保业务运营的高效性和可靠性。

**核心算法原理**：
- **需求预测**：基于历史数据和外部因素的时间序列预测
- **库存优化**：动态安全库存和补货点计算
- **供应商评估**：多维度供应商绩效评价体系
- **风险管理**：供应链风险识别和应对策略

**技术实现方案**：
```javascript
// 供应链管理系统
class SupplyChainManager {
  constructor(config = {}) {
    this.config = {
      forecastHorizon: 90, // 预测天数
      safetyStockDays: 7, // 安全库存天数
      leadTimeDays: 14, // 采购提前期
      serviceLevel: 0.95, // 服务水平
      ...config
    };

    this.suppliers = new Map();
    this.products = new Map();
    this.inventory = new Map();
    this.orders = new Map();
    this.forecasts = new Map();
    this.riskAssessments = new Map();

    this.demandForecaster = new DemandForecaster();
    this.inventoryOptimizer = new InventoryOptimizer();
    this.supplierEvaluator = new SupplierEvaluator();
    this.riskAnalyzer = new RiskAnalyzer();

    this.initializeSystem();
  }

  initializeSystem() {
    // 定期更新预测
    setInterval(() => {
      this.updateDemandForecasts();
    }, 24 * 60 * 60 * 1000); // 每天更新

    // 定期检查库存
    setInterval(() => {
      this.checkInventoryLevels();
    }, 60 * 60 * 1000); // 每小时检查

    // 定期评估供应商
    setInterval(() => {
      this.evaluateSuppliers();
    }, 7 * 24 * 60 * 60 * 1000); // 每周评估
  }

  // 供应商管理
  async addSupplier(supplierData) {
    const supplier = {
      id: supplierData.id,
      name: supplierData.name,
      contact: supplierData.contact,
      location: supplierData.location,
      capabilities: supplierData.capabilities || [],
      certifications: supplierData.certifications || [],
      performance: {
        qualityScore: 0,
        deliveryScore: 0,
        costScore: 0,
        serviceScore: 0,
        overallScore: 0
      },
      riskProfile: {
        financialRisk: 'low',
        operationalRisk: 'low',
        geopoliticalRisk: 'low',
        overallRisk: 'low'
      },
      contracts: [],
      orders: [],
      createdAt: Date.now(),
      lastEvaluated: null
    };

    this.suppliers.set(supplier.id, supplier);

    // 初始评估
    await this.evaluateSupplier(supplier.id);

    return supplier;
  }

  async evaluateSupplier(supplierId) {
    const supplier = this.suppliers.get(supplierId);
    if (!supplier) return;

    const evaluation = await this.supplierEvaluator.evaluate(supplier);

    supplier.performance = evaluation.performance;
    supplier.riskProfile = evaluation.riskProfile;
    supplier.lastEvaluated = Date.now();

    this.suppliers.set(supplierId, supplier);

    // 如果评分过低，触发警告
    if (evaluation.performance.overallScore < 0.6) {
      await this.handleLowPerformingSupplier(supplier);
    }

    return evaluation;
  }

  async handleLowPerformingSupplier(supplier) {
    const actions = [
      {
        type: 'performance_review',
        description: '安排供应商绩效评审会议',
        priority: 'high',
        dueDate: Date.now() + 7 * 24 * 60 * 60 * 1000
      },
      {
        type: 'improvement_plan',
        description: '制定供应商改进计划',
        priority: 'high',
        dueDate: Date.now() + 14 * 24 * 60 * 60 * 1000
      },
      {
        type: 'backup_supplier',
        description: '寻找备用供应商',
        priority: 'medium',
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000
      }
    ];

    // 记录改进行动
    supplier.improvementActions = actions;

    // 发送通知
    await this.sendSupplierAlert(supplier, 'performance_warning');
  }

  // 需求预测
  async updateDemandForecasts() {
    for (const [productId, product] of this.products) {
      try {
        const forecast = await this.demandForecaster.forecast(productId, {
          horizon: this.config.forecastHorizon,
          includeExternalFactors: true
        });

        this.forecasts.set(productId, forecast);

        // 基于预测更新库存策略
        await this.updateInventoryStrategy(productId, forecast);

      } catch (error) {
        console.error(`Failed to forecast demand for product ${productId}:`, error);
      }
    }
  }

  async updateInventoryStrategy(productId, forecast) {
    const product = this.products.get(productId);
    const currentInventory = this.inventory.get(productId);

    if (!product || !currentInventory) return;

    // 计算优化的库存参数
    const optimizedParams = await this.inventoryOptimizer.optimize(productId, {
      forecast: forecast,
      currentInventory: currentInventory,
      leadTime: product.leadTime || this.config.leadTimeDays,
      serviceLevel: this.config.serviceLevel
    });

    // 更新库存策略
    currentInventory.strategy = {
      ...currentInventory.strategy,
      ...optimizedParams,
      lastUpdated: Date.now()
    };

    this.inventory.set(productId, currentInventory);
  }

  // 库存管理
  async checkInventoryLevels() {
    for (const [productId, inventory] of this.inventory) {
      const reorderNeeded = await this.checkReorderPoint(productId, inventory);

      if (reorderNeeded) {
        await this.createPurchaseOrder(productId, reorderNeeded);
      }

      // 检查过期库存
      const expiredItems = await this.checkExpiredInventory(productId, inventory);
      if (expiredItems.length > 0) {
        await this.handleExpiredInventory(productId, expiredItems);
      }
    }
  }

  async checkReorderPoint(productId, inventory) {
    const forecast = this.forecasts.get(productId);
    if (!forecast) return false;

    const currentStock = inventory.currentStock;
    const reorderPoint = inventory.strategy.reorderPoint;
    const maxStock = inventory.strategy.maxStock;

    if (currentStock <= reorderPoint) {
      // 计算订购数量
      const orderQuantity = Math.min(
        maxStock - currentStock,
        inventory.strategy.economicOrderQuantity || (maxStock - reorderPoint)
      );

      return {
        quantity: orderQuantity,
        urgency: currentStock < inventory.strategy.safetyStock ? 'urgent' : 'normal',
        reason: 'reorder_point_reached'
      };
    }

    return false;
  }

  async createPurchaseOrder(productId, orderInfo) {
    const product = this.products.get(productId);
    const preferredSuppliers = await this.getPreferredSuppliers(productId);

    if (preferredSuppliers.length === 0) {
      console.warn(`No suppliers available for product ${productId}`);
      return;
    }

    // 选择最佳供应商
    const selectedSupplier = await this.selectBestSupplier(preferredSuppliers, orderInfo);

    const purchaseOrder = {
      id: `PO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      productId: productId,
      supplierId: selectedSupplier.id,
      quantity: orderInfo.quantity,
      unitPrice: selectedSupplier.pricing.unitPrice,
      totalAmount: orderInfo.quantity * selectedSupplier.pricing.unitPrice,
      urgency: orderInfo.urgency,
      expectedDeliveryDate: Date.now() + selectedSupplier.leadTime * 24 * 60 * 60 * 1000,
      status: 'pending',
      createdAt: Date.now(),
      reason: orderInfo.reason
    };

    this.orders.set(purchaseOrder.id, purchaseOrder);

    // 发送订单给供应商
    await this.sendPurchaseOrder(purchaseOrder);

    // 更新预期库存
    await this.updateExpectedInventory(productId, orderInfo.quantity, purchaseOrder.expectedDeliveryDate);

    return purchaseOrder;
  }

  async selectBestSupplier(suppliers, orderInfo) {
    let bestSupplier = null;
    let bestScore = -1;

    for (const supplier of suppliers) {
      const score = this.calculateSupplierScore(supplier, orderInfo);

      if (score > bestScore) {
        bestScore = score;
        bestSupplier = supplier;
      }
    }

    return bestSupplier;
  }

  calculateSupplierScore(supplier, orderInfo) {
    let score = 0;

    // 性能评分 (40%)
    score += supplier.performance.overallScore * 0.4;

    // 价格评分 (30%)
    const priceScore = 1 - (supplier.pricing.unitPrice / this.getMaxPrice(supplier.productId));
    score += priceScore * 0.3;

    // 交付时间评分 (20%)
    const deliveryScore = orderInfo.urgency === 'urgent' ?
      (1 - supplier.leadTime / 30) : 0.8; // 紧急订单更看重交付时间
    score += deliveryScore * 0.2;

    // 风险评分 (10%)
    const riskScore = this.getRiskScore(supplier.riskProfile.overallRisk);
    score += riskScore * 0.1;

    return Math.max(0, Math.min(1, score));
  }

  getRiskScore(riskLevel) {
    const riskScores = {
      'low': 1.0,
      'medium': 0.7,
      'high': 0.3,
      'critical': 0.1
    };
    return riskScores[riskLevel] || 0.5;
  }

  // 风险管理
  async assessSupplyChainRisks() {
    const risks = [];

    // 供应商风险评估
    for (const [supplierId, supplier] of this.suppliers) {
      const supplierRisks = await this.riskAnalyzer.assessSupplierRisk(supplier);
      risks.push(...supplierRisks);
    }

    // 库存风险评估
    for (const [productId, inventory] of this.inventory) {
      const inventoryRisks = await this.riskAnalyzer.assessInventoryRisk(productId, inventory);
      risks.push(...inventoryRisks);
    }

    // 需求风险评估
    for (const [productId, forecast] of this.forecasts) {
      const demandRisks = await this.riskAnalyzer.assessDemandRisk(productId, forecast);
      risks.push(...demandRisks);
    }

    // 按风险等级排序
    risks.sort((a, b) => this.getRiskPriority(b.level) - this.getRiskPriority(a.level));

    // 存储风险评估结果
    this.riskAssessments.set(Date.now(), {
      timestamp: Date.now(),
      risks: risks,
      summary: this.summarizeRisks(risks)
    });

    // 处理高风险项目
    const highRisks = risks.filter(risk => risk.level === 'high' || risk.level === 'critical');
    for (const risk of highRisks) {
      await this.handleHighRisk(risk);
    }

    return risks;
  }

  getRiskPriority(level) {
    const priorities = {
      'critical': 4,
      'high': 3,
      'medium': 2,
      'low': 1
    };
    return priorities[level] || 0;
  }

  async handleHighRisk(risk) {
    const mitigationPlan = await this.createMitigationPlan(risk);

    // 执行缓解措施
    for (const action of mitigationPlan.actions) {
      await this.executeMitigationAction(action);
    }

    // 发送风险警报
    await this.sendRiskAlert(risk, mitigationPlan);
  }

  async createMitigationPlan(risk) {
    const plan = {
      riskId: risk.id,
      riskType: risk.type,
      actions: [],
      timeline: {},
      responsibleParty: null
    };

    switch (risk.type) {
      case 'supplier_performance':
        plan.actions = [
          { type: 'supplier_review', priority: 'high', deadline: Date.now() + 7 * 24 * 60 * 60 * 1000 },
          { type: 'backup_supplier_activation', priority: 'medium', deadline: Date.now() + 14 * 24 * 60 * 60 * 1000 }
        ];
        break;

      case 'inventory_shortage':
        plan.actions = [
          { type: 'emergency_procurement', priority: 'critical', deadline: Date.now() + 24 * 60 * 60 * 1000 },
          { type: 'demand_management', priority: 'high', deadline: Date.now() + 3 * 24 * 60 * 60 * 1000 }
        ];
        break;

      case 'demand_volatility':
        plan.actions = [
          { type: 'forecast_review', priority: 'high', deadline: Date.now() + 3 * 24 * 60 * 60 * 1000 },
          { type: 'safety_stock_adjustment', priority: 'medium', deadline: Date.now() + 7 * 24 * 60 * 60 * 1000 }
        ];
        break;
    }

    return plan;
  }

  // 性能分析和报告
  async generateSupplyChainReport(timeRange = '30d') {
    const endDate = Date.now();
    const startDate = endDate - this.parseTimeRange(timeRange);

    const report = {
      period: { start: startDate, end: endDate },
      summary: {},
      suppliers: {},
      inventory: {},
      orders: {},
      risks: {},
      recommendations: []
    };

    // 供应商绩效汇总
    report.suppliers = await this.analyzeSupplierPerformance(startDate, endDate);

    // 库存分析
    report.inventory = await this.analyzeInventoryPerformance(startDate, endDate);

    // 订单分析
    report.orders = await this.analyzeOrderPerformance(startDate, endDate);

    // 风险分析
    report.risks = await this.analyzeRiskTrends(startDate, endDate);

    // 生成改进建议
    report.recommendations = await this.generateRecommendations(report);

    // 计算关键指标
    report.summary = this.calculateKPIs(report);

    return report;
  }

  async analyzeSupplierPerformance(startDate, endDate) {
    const analysis = {
      totalSuppliers: this.suppliers.size,
      activeSuppliers: 0,
      averagePerformance: 0,
      topPerformers: [],
      underPerformers: [],
      performanceTrends: {}
    };

    let totalPerformance = 0;
    const supplierPerformances = [];

    for (const [supplierId, supplier] of this.suppliers) {
      const orders = Array.from(this.orders.values())
        .filter(order => order.supplierId === supplierId &&
                order.createdAt >= startDate &&
                order.createdAt <= endDate);

      if (orders.length > 0) {
        analysis.activeSuppliers++;
        totalPerformance += supplier.performance.overallScore;

        supplierPerformances.push({
          id: supplierId,
          name: supplier.name,
          performance: supplier.performance.overallScore,
          orderCount: orders.length,
          onTimeDelivery: this.calculateOnTimeDelivery(orders),
          qualityIssues: this.calculateQualityIssues(orders)
        });
      }
    }

    analysis.averagePerformance = analysis.activeSuppliers > 0 ?
      totalPerformance / analysis.activeSuppliers : 0;

    // 排序并识别顶级和低绩效供应商
    supplierPerformances.sort((a, b) => b.performance - a.performance);
    analysis.topPerformers = supplierPerformances.slice(0, 5);
    analysis.underPerformers = supplierPerformances.filter(s => s.performance < 0.6);

    return analysis;
  }

  calculateKPIs(report) {
    return {
      supplierPerformanceIndex: report.suppliers.averagePerformance,
      inventoryTurnover: report.inventory.turnoverRate,
      orderFulfillmentRate: report.orders.fulfillmentRate,
      averageLeadTime: report.orders.averageLeadTime,
      stockoutRate: report.inventory.stockoutRate,
      riskScore: report.risks.overallRiskScore,
      costSavings: report.orders.costSavings || 0
    };
  }

  async generateRecommendations(report) {
    const recommendations = [];

    // 基于供应商绩效的建议
    if (report.suppliers.averagePerformance < 0.7) {
      recommendations.push({
        type: 'supplier_optimization',
        priority: 'high',
        description: '供应商整体绩效偏低，建议进行供应商优化',
        actions: [
          '评估低绩效供应商',
          '寻找替代供应商',
          '制定供应商改进计划'
        ]
      });
    }

    // 基于库存的建议
    if (report.inventory.stockoutRate > 0.05) {
      recommendations.push({
        type: 'inventory_optimization',
        priority: 'medium',
        description: '缺货率过高，需要优化库存管理',
        actions: [
          '调整安全库存水平',
          '改进需求预测准确性',
          '缩短供应商交付周期'
        ]
      });
    }

    // 基于风险的建议
    if (report.risks.overallRiskScore > 0.6) {
      recommendations.push({
        type: 'risk_mitigation',
        priority: 'high',
        description: '供应链风险较高，需要加强风险管理',
        actions: [
          '建立多元化供应商网络',
          '增加关键物料的安全库存',
          '建立应急响应计划'
        ]
      });
    }

    return recommendations;
  }

  parseTimeRange(timeRange) {
    const ranges = {
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
      '1y': 365 * 24 * 60 * 60 * 1000
    };
    return ranges[timeRange] || ranges['30d'];
  }
}

// 需求预测器
class DemandForecaster {
  async forecast(productId, options = {}) {
    const historicalData = await this.getHistoricalDemand(productId);
    const externalFactors = options.includeExternalFactors ?
      await this.getExternalFactors(productId) : null;

    // 使用多种预测方法
    const forecasts = {
      movingAverage: this.movingAverageForcast(historicalData, options.horizon),
      exponentialSmoothing: this.exponentialSmoothingForecast(historicalData, options.horizon),
      linearRegression: this.linearRegressionForecast(historicalData, options.horizon),
      seasonalDecomposition: this.seasonalForecast(historicalData, options.horizon)
    };

    // 如果有外部因素，使用机器学习模型
    if (externalFactors) {
      forecasts.mlModel = await this.mlForecast(historicalData, externalFactors, options.horizon);
    }

    // 组合预测
    const combinedForecast = this.combineForecast(forecasts);

    return {
      productId,
      horizon: options.horizon,
      forecast: combinedForecast,
      confidence: this.calculateConfidence(forecasts, historicalData),
      methods: Object.keys(forecasts),
      generatedAt: Date.now()
    };
  }

  async getHistoricalDemand(productId) {
    // 模拟获取历史需求数据
    const days = 365;
    const data = [];

    for (let i = days; i > 0; i--) {
      const date = Date.now() - i * 24 * 60 * 60 * 1000;
      const basedemand = 100;
      const trend = i * 0.1; // 轻微上升趋势
      const seasonal = 20 * Math.sin(2 * Math.PI * i / 365); // 年度季节性
      const noise = (Math.random() - 0.5) * 20; // 随机噪声

      data.push({
        date,
        demand: Math.max(0, basedemand + trend + seasonal + noise)
      });
    }

    return data;
  }

  movingAverageForcast(data, horizon) {
    const windowSize = Math.min(30, data.length);
    const recentData = data.slice(-windowSize);
    const average = recentData.reduce((sum, d) => sum + d.demand, 0) / recentData.length;

    return Array.from({ length: horizon }, (_, i) => ({
      date: Date.now() + (i + 1) * 24 * 60 * 60 * 1000,
      demand: average
    }));
  }

  exponentialSmoothingForecast(data, horizon) {
    const alpha = 0.3; // 平滑参数
    let smoothed = data[0].demand;

    // 计算指数平滑值
    for (let i = 1; i < data.length; i++) {
      smoothed = alpha * data[i].demand + (1 - alpha) * smoothed;
    }

    return Array.from({ length: horizon }, (_, i) => ({
      date: Date.now() + (i + 1) * 24 * 60 * 60 * 1000,
      demand: smoothed
    }));
  }

  linearRegressionForecast(data, horizon) {
    // 简单线性回归
    const n = data.length;
    const sumX = data.reduce((sum, _, i) => sum + i, 0);
    const sumY = data.reduce((sum, d) => sum + d.demand, 0);
    const sumXY = data.reduce((sum, d, i) => sum + i * d.demand, 0);
    const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return Array.from({ length: horizon }, (_, i) => ({
      date: Date.now() + (i + 1) * 24 * 60 * 60 * 1000,
      demand: Math.max(0, intercept + slope * (n + i))
    }));
  }

  seasonalForecast(data, horizon) {
    // 简化的季节性分解
    const seasonalPeriod = 365; // 年度季节性
    const seasonal = [];

    // 计算季节性模式
    for (let i = 0; i < seasonalPeriod; i++) {
      const seasonalData = data.filter((_, index) => index % seasonalPeriod === i);
      const seasonalAvg = seasonalData.reduce((sum, d) => sum + d.demand, 0) / seasonalData.length;
      seasonal[i] = seasonalAvg;
    }

    const overallAvg = data.reduce((sum, d) => sum + d.demand, 0) / data.length;

    return Array.from({ length: horizon }, (_, i) => {
      const seasonalIndex = (data.length + i) % seasonalPeriod;
      const seasonalFactor = seasonal[seasonalIndex] / overallAvg;

      return {
        date: Date.now() + (i + 1) * 24 * 60 * 60 * 1000,
        demand: overallAvg * seasonalFactor
      };
    });
  }

  combineForecast(forecasts) {
    const methods = Object.keys(forecasts);
    const horizon = forecasts[methods[0]].length;
    const combined = [];

    for (let i = 0; i < horizon; i++) {
      let totalDemand = 0;
      let totalWeight = 0;

      // 加权平均组合
      for (const method of methods) {
        const weight = this.getMethodWeight(method);
        totalDemand += forecasts[method][i].demand * weight;
        totalWeight += weight;
      }

      combined.push({
        date: forecasts[methods[0]][i].date,
        demand: totalWeight > 0 ? totalDemand / totalWeight : 0
      });
    }

    return combined;
  }

  getMethodWeight(method) {
    const weights = {
      movingAverage: 0.2,
      exponentialSmoothing: 0.25,
      linearRegression: 0.2,
      seasonalDecomposition: 0.25,
      mlModel: 0.3
    };
    return weights[method] || 0.2;
  }

  calculateConfidence(forecasts, historicalData) {
    // 基于历史预测准确性计算置信度
    const baseConfidence = 0.7;
    const dataQuality = Math.min(1, historicalData.length / 365);
    const methodDiversity = Object.keys(forecasts).length / 5;

    return Math.min(0.95, baseConfidence * dataQuality * methodDiversity);
  }
}

// 库存优化器
class InventoryOptimizer {
  async optimize(productId, options) {
    const { forecast, currentInventory, leadTime, serviceLevel } = options;

    // 计算需求统计
    const demandStats = this.calculateDemandStatistics(forecast);

    // 计算安全库存
    const safetyStock = this.calculateSafetyStock(demandStats, leadTime, serviceLevel);

    // 计算再订货点
    const reorderPoint = this.calculateReorderPoint(demandStats, leadTime, safetyStock);

    // 计算经济订货量
    const economicOrderQuantity = this.calculateEOQ(demandStats, currentInventory);

    // 计算最大库存
    const maxStock = reorderPoint + economicOrderQuantity;

    return {
      safetyStock,
      reorderPoint,
      economicOrderQuantity,
      maxStock,
      targetServiceLevel: serviceLevel,
      optimizedAt: Date.now()
    };
  }

  calculateDemandStatistics(forecast) {
    const demands = forecast.forecast.map(f => f.demand);
    const mean = demands.reduce((sum, d) => sum + d, 0) / demands.length;
    const variance = demands.reduce((sum, d) => sum + Math.pow(d - mean, 2), 0) / demands.length;
    const stdDev = Math.sqrt(variance);

    return { mean, variance, stdDev, min: Math.min(...demands), max: Math.max(...demands) };
  }

  calculateSafetyStock(demandStats, leadTime, serviceLevel) {
    // 使用正态分布假设计算安全库存
    const zScore = this.getZScore(serviceLevel);
    const leadTimeDemandStdDev = demandStats.stdDev * Math.sqrt(leadTime);

    return Math.ceil(zScore * leadTimeDemandStdDev);
  }

  getZScore(serviceLevel) {
    // 简化的Z分数查找
    const zScores = {
      0.90: 1.28,
      0.95: 1.65,
      0.99: 2.33
    };

    return zScores[serviceLevel] || 1.65;
  }

  calculateReorderPoint(demandStats, leadTime, safetyStock) {
    const leadTimeDemand = demandStats.mean * leadTime;
    return Math.ceil(leadTimeDemand + safetyStock);
  }

  calculateEOQ(demandStats, currentInventory) {
    // 简化的经济订货量计算
    const annualDemand = demandStats.mean * 365;
    const orderingCost = currentInventory.orderingCost || 100;
    const holdingCost = currentInventory.holdingCost || 10;

    return Math.ceil(Math.sqrt((2 * annualDemand * orderingCost) / holdingCost));
  }
}

// 供应商评估器
class SupplierEvaluator {
  async evaluate(supplier) {
    const performance = await this.evaluatePerformance(supplier);
    const riskProfile = await this.evaluateRisk(supplier);

    return {
      performance,
      riskProfile,
      evaluatedAt: Date.now()
    };
  }

  async evaluatePerformance(supplier) {
    // 模拟性能评估
    const qualityScore = this.calculateQualityScore(supplier);
    const deliveryScore = this.calculateDeliveryScore(supplier);
    const costScore = this.calculateCostScore(supplier);
    const serviceScore = this.calculateServiceScore(supplier);

    const overallScore = (qualityScore + deliveryScore + costScore + serviceScore) / 4;

    return {
      qualityScore,
      deliveryScore,
      costScore,
      serviceScore,
      overallScore
    };
  }

  calculateQualityScore(supplier) {
    // 基于质量指标计算分数
    const defectRate = supplier.qualityMetrics?.defectRate || 0.02;
    return Math.max(0, 1 - defectRate * 10);
  }

  calculateDeliveryScore(supplier) {
    // 基于交付表现计算分数
    const onTimeRate = supplier.deliveryMetrics?.onTimeRate || 0.9;
    return onTimeRate;
  }

  calculateCostScore(supplier) {
    // 基于成本竞争力计算分数
    const costCompetitiveness = supplier.costMetrics?.competitiveness || 0.8;
    return costCompetitiveness;
  }

  calculateServiceScore(supplier) {
    // 基于服务质量计算分数
    const responseTime = supplier.serviceMetrics?.responseTime || 24;
    return Math.max(0, 1 - responseTime / 48);
  }

  async evaluateRisk(supplier) {
    const financialRisk = this.assessFinancialRisk(supplier);
    const operationalRisk = this.assessOperationalRisk(supplier);
    const geopoliticalRisk = this.assessGeopoliticalRisk(supplier);

    const overallRisk = this.calculateOverallRisk(financialRisk, operationalRisk, geopoliticalRisk);

    return {
      financialRisk,
      operationalRisk,
      geopoliticalRisk,
      overallRisk
    };
  }

  assessFinancialRisk(supplier) {
    // 简化的财务风险评估
    const creditRating = supplier.financialInfo?.creditRating || 'B';
    const riskLevels = { 'AAA': 'low', 'AA': 'low', 'A': 'low', 'BBB': 'medium', 'BB': 'medium', 'B': 'high', 'C': 'critical' };
    return riskLevels[creditRating] || 'medium';
  }

  assessOperationalRisk(supplier) {
    // 基于运营指标评估风险
    const capacityUtilization = supplier.operationalInfo?.capacityUtilization || 0.8;
    if (capacityUtilization > 0.95) return 'high';
    if (capacityUtilization > 0.85) return 'medium';
    return 'low';
  }

  assessGeopoliticalRisk(supplier) {
    // 基于地理位置评估地缘政治风险
    const location = supplier.location?.country || 'Unknown';
    const riskCountries = ['Country1', 'Country2']; // 高风险国家列表

    if (riskCountries.includes(location)) return 'high';
    return 'low';
  }

  calculateOverallRisk(financial, operational, geopolitical) {
    const riskScores = { 'low': 1, 'medium': 2, 'high': 3, 'critical': 4 };
    const avgScore = (riskScores[financial] + riskScores[operational] + riskScores[geopolitical]) / 3;

    if (avgScore >= 3.5) return 'critical';
    if (avgScore >= 2.5) return 'high';
    if (avgScore >= 1.5) return 'medium';
    return 'low';
  }
}

// 使用示例
const scm = new SupplyChainManager({
  forecastHorizon: 90,
  safetyStockDays: 14,
  serviceLevel: 0.95
});

// 添加供应商
async function setupSupplyChain() {
  const supplier = await scm.addSupplier({
    id: 'supplier_001',
    name: 'AI组件供应商',
    contact: { email: '<EMAIL>', phone: '+86-123-4567-8900' },
    location: { country: 'China', city: 'Shenzhen' },
    capabilities: ['AI芯片', '传感器', '软件开发'],
    certifications: ['ISO9001', 'ISO14001'],
    qualityMetrics: { defectRate: 0.01 },
    deliveryMetrics: { onTimeRate: 0.95 },
    costMetrics: { competitiveness: 0.85 },
    serviceMetrics: { responseTime: 12 }
  });

  console.log('供应商添加成功:', supplier);

  // 添加产品
  scm.products.set('product_001', {
    id: 'product_001',
    name: 'AI处理芯片',
    category: '电子元件',
    leadTime: 21,
    unitCost: 500,
    suppliers: ['supplier_001']
  });

  // 初始化库存
  scm.inventory.set('product_001', {
    productId: 'product_001',
    currentStock: 1000,
    strategy: {
      safetyStock: 200,
      reorderPoint: 500,
      maxStock: 2000,
      economicOrderQuantity: 500
    },
    orderingCost: 200,
    holdingCost: 25
  });

  // 生成需求预测
  await scm.updateDemandForecasts();

  // 评估供应链风险
  const risks = await scm.assessSupplyChainRisks();
  console.log('供应链风险评估:', risks);

  // 生成供应链报告
  const report = await scm.generateSupplyChainReport('30d');
  console.log('供应链报告:', report);
}
```

**技术要点**：
- 智能需求预测和库存优化
- 多维度供应商绩效评估
- 自动化风险识别和缓解
- 数据驱动的决策支持

**实践项目**：
- 构建智能供应链管理平台
- 实现需求预测分析系统
- 开发供应商风险监控系统
